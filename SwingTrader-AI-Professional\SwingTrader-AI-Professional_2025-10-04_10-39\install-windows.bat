@echo off
setlocal enabledelayedexpansion
title SwingTrader AI - Professional Installation

REM Set colors for better visual experience
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[92m"
set "RED=%ESC%[91m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

cls
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                    🚀 SwingTrader AI - Professional Setup                    ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                     Professional Swing Trading Platform                     ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %WHITE%Welcome to SwingTrader AI - Your Professional Trading Platform%RESET%
echo %WHITE%This installer will set up everything you need to start swing trading.%RESET%
echo.

REM Check Windows version
echo %BLUE%[1/7] Checking system compatibility...%RESET%
ver | findstr /i "10\|11" >nul
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  Warning: This application is optimized for Windows 10/11%RESET%
    echo %YELLOW%   It may work on older versions but performance is not guaranteed.%RESET%
    echo.
)

REM Check if running as administrator (optional but recommended)
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%💡 Tip: For best results, consider running as Administrator%RESET%
    echo %YELLOW%   (Right-click this file and select "Run as administrator")%RESET%
    echo.
)

REM Check Node.js installation with detailed version info
echo %BLUE%[2/7] Checking Node.js installation...%RESET%
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%❌ Node.js is not installed!%RESET%
    echo.
    echo %WHITE%Node.js is required to run SwingTrader AI.%RESET%
    echo.
    echo %CYAN%Would you like to install Node.js automatically? (Y/N)%RESET%
    set /p INSTALL_NODEJS=
    if /i "!INSTALL_NODEJS!"=="Y" goto :install_nodejs
    if /i "!INSTALL_NODEJS!"=="YES" goto :install_nodejs

    echo.
    echo %WHITE%Please install Node.js manually:%RESET%
    echo %CYAN%1. Visit: https://nodejs.org/%RESET%
    echo %CYAN%2. Download the LTS version (recommended)%RESET%
    echo %CYAN%3. Run the installer with default settings%RESET%
    echo %CYAN%4. Restart your computer%RESET%
    echo %CYAN%5. Run this installer again%RESET%
    echo.
    echo %YELLOW%Opening Node.js download page...%RESET%
    start https://nodejs.org/
    echo.
    pause
    exit /b 1

    :install_nodejs
    echo.
    echo %BLUE%Launching Node.js installer helper...%RESET%
    if exist "nodejs-installer.bat" (
        call "nodejs-installer.bat"
        if %errorlevel% neq 0 (
            echo %RED%❌ Node.js installation failed%RESET%
            pause
            exit /b 1
        )
        REM Re-check Node.js after installation
        node --version >nul 2>&1
        if %errorlevel% neq 0 (
            echo %YELLOW%⚠️  Please restart your computer and run this installer again%RESET%
            pause
            exit /b 1
        )
    ) else (
        echo %RED%❌ Node.js installer helper not found%RESET%
        echo %WHITE%Please install Node.js manually from https://nodejs.org/%RESET%
        start https://nodejs.org/
        pause
        exit /b 1
    )
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %GREEN%✅ Node.js found: %NODE_VERSION%%RESET%

REM Check npm version
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo %GREEN%✅ npm found: v%NPM_VERSION%%RESET%
echo.

REM Check internet connection
echo %BLUE%[3/7] Checking internet connection...%RESET%
ping -n 1 google.com >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%⚠️  Warning: Internet connection may be limited%RESET%
    echo %YELLOW%   Installation may take longer or fail%RESET%
    echo.
) else (
    echo %GREEN%✅ Internet connection verified%RESET%
)

REM Install dependencies with progress indication
echo.
echo %BLUE%[4/7] Installing application dependencies...%RESET%
echo %WHITE%This may take 2-5 minutes depending on your internet speed...%RESET%
echo.

call npm install --silent
if %errorlevel% neq 0 (
    echo %RED%❌ Failed to install dependencies%RESET%
    echo.
    echo %WHITE%Troubleshooting steps:%RESET%
    echo %CYAN%1. Check your internet connection%RESET%
    echo %CYAN%2. Try running as Administrator%RESET%
    echo %CYAN%3. Temporarily disable antivirus%RESET%
    echo %CYAN%4. Clear npm cache: npm cache clean --force%RESET%
    echo.
    pause
    exit /b 1
)

echo %GREEN%✅ Dependencies installed successfully%RESET%
echo.

REM Environment configuration with enhanced guidance
echo %BLUE%[5/7] Configuring API keys...%RESET%
if not exist ".env.local" (
    if exist ".env.local.example" (
        copy ".env.local.example" ".env.local" >nul
        echo %GREEN%✅ Environment template created%RESET%
    ) else (
        echo %YELLOW%⚠️  Creating default environment file...%RESET%
        echo # SwingTrader AI - API Configuration > .env.local
        echo # Replace with your actual API keys >> .env.local
        echo. >> .env.local
        echo # Financial Data APIs >> .env.local
        echo POLYGON_API_KEY=your_polygon_api_key_here >> .env.local
        echo FMP_API_KEY=your_fmp_api_key_here >> .env.local
        echo. >> .env.local
        echo # OpenAI API (for AI analysis) >> .env.local
        echo OPENAI_API_KEY=your_openai_api_key_here >> .env.local
        echo. >> .env.local
        echo # Application Settings >> .env.local
        echo NEXT_PUBLIC_APP_URL=http://localhost:3000 >> .env.local
    )

    echo.
    echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
    echo %CYAN%║                            🔑 API KEYS REQUIRED                              ║%RESET%
    echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
    echo.
    echo %WHITE%You need to add your API keys to continue. Here's where to get them:%RESET%
    echo.
    echo %YELLOW%📊 POLYGON.IO (Market Data):%RESET%
    echo %WHITE%   • Visit: https://polygon.io/%RESET%
    echo %WHITE%   • Sign up for free account%RESET%
    echo %WHITE%   • Get your API key from dashboard%RESET%
    echo.
    echo %YELLOW%📈 FINANCIAL MODELING PREP (Additional Data):%RESET%
    echo %WHITE%   • Visit: https://financialmodelingprep.com/%RESET%
    echo %WHITE%   • Sign up for free account%RESET%
    echo %WHITE%   • Get your API key from dashboard%RESET%
    echo.
    echo %YELLOW%🤖 OPENAI (AI Analysis):%RESET%
    echo %WHITE%   • Visit: https://platform.openai.com/%RESET%
    echo %WHITE%   • Create account and add billing%RESET%
    echo %WHITE%   • Generate API key%RESET%
    echo.
    echo %CYAN%Opening configuration file for editing...%RESET%
    timeout /t 2 >nul
    notepad .env.local
    echo.
    echo %WHITE%Please save the file after adding your API keys.%RESET%
    echo %YELLOW%Press any key when you've finished editing...%RESET%
    pause >nul
) else (
    echo %GREEN%✅ Environment file already exists%RESET%
)

echo.
echo %BLUE%[6/7] Building application...%RESET%
echo %WHITE%Optimizing for production... This may take 1-2 minutes...%RESET%
echo.

call npm run build --silent
if %errorlevel% neq 0 (
    echo %RED%❌ Build failed%RESET%
    echo.
    echo %WHITE%This usually means:%RESET%
    echo %CYAN%1. API keys are missing or invalid%RESET%
    echo %CYAN%2. Dependencies weren't installed properly%RESET%
    echo %CYAN%3. Node.js version is too old%RESET%
    echo.
    echo %WHITE%Try running the installer again or check the API keys.%RESET%
    pause
    exit /b 1
)

echo %GREEN%✅ Application built successfully%RESET%
echo.

REM Create enhanced startup script
echo %BLUE%[7/7] Creating startup shortcuts...%RESET%

echo @echo off > "🚀 Start SwingTrader AI.bat"
echo title SwingTrader AI - Professional Trading Platform >> "🚀 Start SwingTrader AI.bat"
echo cls >> "🚀 Start SwingTrader AI.bat"
echo echo. >> "🚀 Start SwingTrader AI.bat"
echo echo ╔══════════════════════════════════════════════════════════════════════════════╗ >> "🚀 Start SwingTrader AI.bat"
echo echo ║                                                                              ║ >> "🚀 Start SwingTrader AI.bat"
echo echo ║                    🚀 SwingTrader AI - Starting Up...                        ║ >> "🚀 Start SwingTrader AI.bat"
echo echo ║                                                                              ║ >> "🚀 Start SwingTrader AI.bat"
echo echo ╚══════════════════════════════════════════════════════════════════════════════╝ >> "🚀 Start SwingTrader AI.bat"
echo echo. >> "🚀 Start SwingTrader AI.bat"
echo echo 🌐 Application will open at: http://localhost:3000 >> "🚀 Start SwingTrader AI.bat"
echo echo 🛑 Press Ctrl+C to stop the server >> "🚀 Start SwingTrader AI.bat"
echo echo 📱 Access from any device on your network >> "🚀 Start SwingTrader AI.bat"
echo echo. >> "🚀 Start SwingTrader AI.bat"
echo timeout /t 3 ^>nul >> "🚀 Start SwingTrader AI.bat"
echo start http://localhost:3000 >> "🚀 Start SwingTrader AI.bat"
echo call npm start >> "🚀 Start SwingTrader AI.bat"

REM Create update script
echo @echo off > "🔄 Update SwingTrader AI.bat"
echo title SwingTrader AI - Update >> "🔄 Update SwingTrader AI.bat"
echo echo Updating SwingTrader AI... >> "🔄 Update SwingTrader AI.bat"
echo call npm install >> "🔄 Update SwingTrader AI.bat"
echo call npm run build >> "🔄 Update SwingTrader AI.bat"
echo echo Update complete! >> "🔄 Update SwingTrader AI.bat"
echo pause >> "🔄 Update SwingTrader AI.bat"

echo %GREEN%✅ Startup scripts created%RESET%
echo.

REM Final success message
cls
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                        🎉 INSTALLATION COMPLETE! 🎉                         ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %GREEN%SwingTrader AI is now ready to use!%RESET%
echo.
echo %WHITE%🚀 TO START TRADING:%RESET%
echo %CYAN%   Double-click: "🚀 Start SwingTrader AI.bat"%RESET%
echo %CYAN%   Or run: npm start%RESET%
echo.
echo %WHITE%🌐 ACCESS YOUR PLATFORM:%RESET%
echo %CYAN%   Local: http://localhost:3000%RESET%
echo %CYAN%   Network: http://[your-ip]:3000%RESET%
echo.
echo %WHITE%🔄 TO UPDATE LATER:%RESET%
echo %CYAN%   Double-click: "🔄 Update SwingTrader AI.bat"%RESET%
echo.
echo %WHITE%📊 FEATURES INCLUDED:%RESET%
echo %CYAN%   ✅ AI-Powered Trade Analysis%RESET%
echo %CYAN%   ✅ Real-time Market Scanning%RESET%
echo %CYAN%   ✅ Professional Trading Cards%RESET%
echo %CYAN%   ✅ Risk Management Tools%RESET%
echo %CYAN%   ✅ Paper Trading System%RESET%
echo.
echo %WHITE%🎯 READY TO TRADE PROFESSIONALLY!%RESET%
echo.
echo %YELLOW%Press any key to launch SwingTrader AI now...%RESET%
pause >nul

REM Auto-launch the application
start "" "🚀 Start SwingTrader AI.bat"
