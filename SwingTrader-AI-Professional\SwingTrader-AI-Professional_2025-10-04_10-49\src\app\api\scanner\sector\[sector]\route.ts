import { NextRequest, NextResponse } from 'next/server'
import { swingScanner } from '@/lib/swingScanner'
import { STOCKS_BY_SECTOR } from '@/data/watchlist'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sector: string }> }
) {
  try {
    const { sector } = await params
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    
    // Decode sector name (handle URL encoding)
    const decodedSector = decodeURIComponent(sector)
    
    // Find stocks in the specified sector
    const sectorStocks = STOCKS_BY_SECTOR[decodedSector]
    
    if (!sectorStocks || sectorStocks.length === 0) {
      return NextResponse.json(
        { error: `No stocks found for sector: ${decodedSector}` },
        { status: 404 }
      )
    }
    
    const symbols = sectorStocks.map(stock => stock.symbol)
    console.log(`Starting sector scan for ${decodedSector}: ${symbols.length} stocks...`)
    
    const scanSummary = await swingScanner.scanStocks(symbols, 5)
    
    // Limit results if requested
    const limitedResults = {
      ...scanSummary,
      sector: decodedSector,
      topOpportunities: scanSummary.topOpportunities.slice(0, limit)
    }
    
    return NextResponse.json(limitedResults)
  } catch (error) {
    console.error('Error in sector scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform sector stock scan' },
      { status: 500 }
    )
  }
}
