export interface StockData {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  marketCap?: number
  pe?: number
  dividend?: number
}

export interface CandlestickData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TechnicalIndicator {
  name: string
  value: number
  signal: 'BUY' | 'SELL' | 'NEUTRAL'
  description: string
}

export interface SwingTradingAnalysis {
  symbol: string
  timeframe: string
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'
  confidence: number
  entryPrice: number
  stopLoss: number
  takeProfit: number
  riskRewardRatio: number
  indicators: TechnicalIndicator[]
  supportLevels: number[]
  resistanceLevels: number[]
  analysis: string
  recommendation: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' | 'NO_TRADE'
}

export interface TradeLog {
  id: string
  userId: string
  symbol: string
  entryPrice: number
  exitPrice?: number
  stopLoss: number
  takeProfit: number
  quantity: number
  side: 'LONG' | 'SHORT'
  status: 'OPEN' | 'CLOSED' | 'CANCELLED'
  entryDate: string
  exitDate?: string
  pnl?: number
  notes?: string
}

export interface SwingTradingStrategy {
  id: string
  name: string
  description: string
  indicators: string[]
  timeframes: string[]
  riskRewardMin: number
  maxRiskPercent: number
}

export interface User {
  id: string
  email: string
  accountSize: number
  riskTolerance: number
  preferredTimeframes: string[]
  createdAt: string
}
