@echo off
setlocal enabledelayedexpansion
title Node.js Installation Helper - SwingTrader AI

REM Set colors for better visual experience
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[92m"
set "RED=%ESC%[91m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

cls
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                    🚀 Node.js Installation Helper                           ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                        SwingTrader AI Setup Assistant                       ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %WHITE%This helper will guide you through installing Node.js for SwingTrader AI%RESET%
echo.

REM Check if Node.js is already installed
echo %BLUE%Checking for existing Node.js installation...%RESET%
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo %GREEN%✅ Node.js is already installed: !NODE_VERSION!%RESET%
    echo.
    
    REM Check if version is adequate (v18+)
    echo !NODE_VERSION! | findstr /r "v1[89]\|v[2-9][0-9]" >nul
    if %errorlevel% equ 0 (
        echo %GREEN%✅ Version is compatible with SwingTrader AI%RESET%
        echo.
        echo %WHITE%You can now run the main installer:%RESET%
        echo %CYAN%   Double-click: install-windows.bat%RESET%
        echo.
        pause
        exit /b 0
    ) else (
        echo %YELLOW%⚠️  Your Node.js version may be too old%RESET%
        echo %YELLOW%   SwingTrader AI requires Node.js v18.0 or higher%RESET%
        echo %YELLOW%   Current version: !NODE_VERSION!%RESET%
        echo.
        echo %WHITE%Would you like to update Node.js? (Y/N)%RESET%
        set /p UPDATE_CHOICE=
        if /i "!UPDATE_CHOICE!"=="Y" goto :download_nodejs
        if /i "!UPDATE_CHOICE!"=="YES" goto :download_nodejs
        echo.
        echo %YELLOW%You may experience issues with older Node.js versions%RESET%
        pause
        exit /b 1
    )
)

echo %RED%❌ Node.js is not installed%RESET%
echo.

:download_nodejs
echo %WHITE%Node.js is required to run SwingTrader AI. Here's what we'll do:%RESET%
echo.
echo %CYAN%1. Download the latest LTS version of Node.js%RESET%
echo %CYAN%2. Guide you through the installation%RESET%
echo %CYAN%3. Verify the installation%RESET%
echo %CYAN%4. Launch the SwingTrader AI installer%RESET%
echo.

REM Detect system architecture
echo %BLUE%Detecting your system...%RESET%
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=x64
    echo %GREEN%✅ 64-bit Windows detected%RESET%
) else if "%PROCESSOR_ARCHITEW6432%"=="AMD64" (
    set ARCH=x64
    echo %GREEN%✅ 64-bit Windows detected%RESET%
) else (
    set ARCH=x86
    echo %YELLOW%⚠️  32-bit Windows detected%RESET%
    echo %YELLOW%   Note: 32-bit support may be limited%RESET%
)
echo.

REM Get the latest LTS version info
echo %BLUE%Preparing download...%RESET%
set NODEJS_URL=https://nodejs.org/dist/v20.18.0/node-v20.18.0-!ARCH!.msi
set INSTALLER_NAME=nodejs-installer.msi

echo %WHITE%Download Details:%RESET%
echo %CYAN%   Version: Node.js v20.18.0 LTS%RESET%
echo %CYAN%   Architecture: !ARCH!%RESET%
echo %CYAN%   Size: ~30MB%RESET%
echo.

echo %WHITE%Press any key to start the download...%RESET%
pause >nul

REM Download Node.js installer
echo %BLUE%Downloading Node.js installer...%RESET%
echo %WHITE%This may take a few minutes depending on your internet speed...%RESET%
echo.

REM Use PowerShell to download the file
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%NODEJS_URL%' -OutFile '%INSTALLER_NAME%' -UseBasicParsing}"

if %errorlevel% neq 0 (
    echo %RED%❌ Download failed!%RESET%
    echo.
    echo %WHITE%Possible solutions:%RESET%
    echo %CYAN%1. Check your internet connection%RESET%
    echo %CYAN%2. Temporarily disable firewall/antivirus%RESET%
    echo %CYAN%3. Try downloading manually from: https://nodejs.org/%RESET%
    echo.
    echo %YELLOW%Opening Node.js website for manual download...%RESET%
    start https://nodejs.org/
    pause
    exit /b 1
)

echo %GREEN%✅ Download completed successfully!%RESET%
echo.

REM Run the installer
echo %BLUE%Starting Node.js installation...%RESET%
echo.
echo %WHITE%The Node.js installer will now open. Please:%RESET%
echo %CYAN%1. Accept the license agreement%RESET%
echo %CYAN%2. Use the default installation path%RESET%
echo %CYAN%3. Keep all default options selected%RESET%
echo %CYAN%4. Click "Install" and wait for completion%RESET%
echo %CYAN%5. Restart your computer when prompted%RESET%
echo.
echo %YELLOW%Press any key to launch the installer...%RESET%
pause >nul

start /wait msiexec /i "%INSTALLER_NAME%" /passive

if %errorlevel% neq 0 (
    echo %RED%❌ Installation may have failed%RESET%
    echo.
    echo %WHITE%Try running the installer manually:%RESET%
    echo %CYAN%   Double-click: %INSTALLER_NAME%%RESET%
    echo.
    pause
    exit /b 1
)

echo %GREEN%✅ Node.js installation completed!%RESET%
echo.

REM Clean up installer file
if exist "%INSTALLER_NAME%" (
    echo %BLUE%Cleaning up installer file...%RESET%
    del "%INSTALLER_NAME%"
)

REM Verify installation
echo %BLUE%Verifying Node.js installation...%RESET%
echo %WHITE%Note: You may need to restart your computer for Node.js to work%RESET%
echo.

REM Refresh environment variables
call refreshenv >nul 2>&1

REM Test Node.js
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo %GREEN%✅ Node.js is working: !NODE_VERSION!%RESET%
    
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo %GREEN%✅ npm is working: v!NPM_VERSION!%RESET%
    echo.
    
    echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
    echo %CYAN%║                                                                              ║%RESET%
    echo %CYAN%║                        🎉 INSTALLATION SUCCESSFUL! 🎉                       ║%RESET%
    echo %CYAN%║                                                                              ║%RESET%
    echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
    echo.
    echo %GREEN%Node.js has been successfully installed!%RESET%
    echo.
    echo %WHITE%🚀 NEXT STEPS:%RESET%
    echo %CYAN%   1. Close this window%RESET%
    echo %CYAN%   2. Double-click: install-windows.bat%RESET%
    echo %CYAN%   3. Follow the SwingTrader AI setup process%RESET%
    echo.
    echo %WHITE%🎯 READY TO INSTALL SWINGTRADER AI!%RESET%
    echo.
    echo %YELLOW%Press any key to launch the main installer now...%RESET%
    pause >nul
    
    REM Launch the main installer
    if exist "install-windows.bat" (
        start "" "install-windows.bat"
    ) else (
        echo %YELLOW%Main installer not found in current directory%RESET%
        echo %WHITE%Please navigate to the SwingTrader AI folder and run install-windows.bat%RESET%
        pause
    )
    
) else (
    echo %YELLOW%⚠️  Node.js installation completed but not detected%RESET%
    echo.
    echo %WHITE%This usually means you need to restart your computer.%RESET%
    echo.
    echo %CYAN%Please:%RESET%
    echo %CYAN%1. Restart your computer%RESET%
    echo %CYAN%2. Run install-windows.bat after restart%RESET%
    echo.
    pause
)

exit /b 0
