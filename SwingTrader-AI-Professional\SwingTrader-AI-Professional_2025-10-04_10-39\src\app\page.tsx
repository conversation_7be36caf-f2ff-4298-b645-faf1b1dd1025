'use client'

import { useState } from 'react'
import { <PERSON><PERSON>ding<PERSON>p, BarChart3, Target, Shield, Brain, Zap, Search, Loader2, Scan } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SwingTradingAnalysis, StockData } from '@/types/trading'
import { formatCurrency, formatPercentage } from '@/lib/utils'
import { SwingScanner } from '@/components/SwingScanner'
import { StrategyScanner } from '@/components/StrategyScanner'

export default function Home() {
  const [selectedSymbol, setSelectedSymbol] = useState('SPY')
  const [customSymbol, setCustomSymbol] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<SwingTradingAnalysis | null>(null)
  const [stockData, setStockData] = useState<StockData | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'individual' | 'scanner' | 'strategies'>('strategies')

  const popularSymbols = ['SPY', 'QQQ', 'AAPL', 'TSLA', 'NVDA', 'MSFT', 'AMZN', 'GOOGL']

  const handleAnalysis = async (symbol: string) => {
    setIsAnalyzing(true)
    setError(null)
    setAnalysis(null)
    setStockData(null)

    try {
      // Fetch stock quote and analysis in parallel
      const [quoteResponse, analysisResponse] = await Promise.all([
        fetch(`/api/stocks/quote/${symbol}`),
        fetch(`/api/analysis/swing/${symbol}`)
      ])

      if (!quoteResponse.ok) {
        const quoteError = await quoteResponse.text()
        console.error('Quote API error:', quoteError)
        throw new Error(`Failed to fetch quote data: ${quoteResponse.status}`)
      }

      if (!analysisResponse.ok) {
        const analysisError = await analysisResponse.text()
        console.error('Analysis API error:', analysisError)
        throw new Error(`Failed to fetch analysis data: ${analysisResponse.status}`)
      }

      const [quoteData, analysisData] = await Promise.all([
        quoteResponse.json(),
        analysisResponse.json()
      ])

      setStockData(quoteData)
      setAnalysis(analysisData)
    } catch (err: any) {
      const errorMessage = err.message?.includes('rate limit') || err.message?.includes('429')
        ? 'API rate limit reached. Please wait a moment and try again.'
        : 'Failed to analyze stock. Please try again.'
      setError(errorMessage)
      console.error('Analysis error:', err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleCustomSymbolSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (customSymbol.trim()) {
      handleAnalysis(customSymbol.toUpperCase())
      setSelectedSymbol(customSymbol.toUpperCase())
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-800 bg-slate-900/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-blue-400" />
              <h1 className="text-2xl font-bold text-white">SwingTrader AI</h1>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <button
                onClick={() => setActiveTab('strategies')}
                className={`transition-colors ${activeTab === 'strategies' ? 'text-white' : 'text-slate-300 hover:text-white'}`}
              >
                Pro Strategies
              </button>
              <button
                onClick={() => setActiveTab('scanner')}
                className={`transition-colors ${activeTab === 'scanner' ? 'text-white' : 'text-slate-300 hover:text-white'}`}
              >
                Basic Scanner
              </button>
              <button
                onClick={() => setActiveTab('individual')}
                className={`transition-colors ${activeTab === 'individual' ? 'text-white' : 'text-slate-300 hover:text-white'}`}
              >
                Individual Analysis
              </button>
              <Button variant="outline" className="border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white">
                Sign In
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-5xl font-bold text-white mb-6">
            AI-Powered Swing Trading Analysis
          </h2>
          <p className="text-xl text-slate-300 mb-8 max-w-3xl mx-auto">
            Professional swing trading strategies with automated scanning, precise entry/exit rules,
            and risk management based on proven methodologies.
          </p>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-slate-800/50 rounded-lg p-1 flex">
              <button
                onClick={() => setActiveTab('strategies')}
                className={`px-6 py-3 rounded-md transition-all ${
                  activeTab === 'strategies'
                    ? 'bg-blue-600 text-white'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <Zap className="inline mr-2 h-4 w-4" />
                Pro Strategies
              </button>
              <button
                onClick={() => setActiveTab('scanner')}
                className={`px-6 py-3 rounded-md transition-all ${
                  activeTab === 'scanner'
                    ? 'bg-blue-600 text-white'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <Scan className="inline mr-2 h-4 w-4" />
                Basic Scanner
              </button>
              <button
                onClick={() => setActiveTab('individual')}
                className={`px-6 py-3 rounded-md transition-all ${
                  activeTab === 'individual'
                    ? 'bg-blue-600 text-white'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <Search className="inline mr-2 h-4 w-4" />
                Individual Analysis
              </button>
            </div>
          </div>

          {/* Content based on active tab */}
          {activeTab === 'strategies' ? (
            <div className="mb-8">
              <h3 className="text-lg text-slate-300 mb-4">Professional Swing Trading Strategies</h3>
              <p className="text-slate-400 mb-6">
                Overnight Momentum & Technical Breakout strategies with precise entry/exit rules and position sizing
              </p>
            </div>
          ) : activeTab === 'scanner' ? (
            <div className="mb-8">
              <h3 className="text-lg text-slate-300 mb-4">Basic Swing Trading Scanner</h3>
              <p className="text-slate-400 mb-6">
                General swing trading analysis with technical indicators and trend detection
              </p>
            </div>
          ) : (
            <div className="mb-8">
              <h3 className="text-lg text-slate-300 mb-4">Individual Stock Analysis</h3>
              <div className="flex flex-wrap justify-center gap-2 mb-6">
                {popularSymbols.map((symbol) => (
                  <Button
                    key={symbol}
                    variant={selectedSymbol === symbol ? "default" : "outline"}
                    onClick={() => {
                      setSelectedSymbol(symbol)
                      handleAnalysis(symbol)
                    }}
                    disabled={isAnalyzing}
                    className={selectedSymbol === symbol
                      ? "bg-blue-600 hover:bg-blue-700"
                      : "border-slate-600 text-slate-300 hover:bg-slate-800"
                    }
                  >
                    {symbol}
                  </Button>
                ))}
              </div>

              {/* Custom Symbol Input */}
              <form onSubmit={handleCustomSymbolSubmit} className="flex justify-center gap-2 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Enter symbol (e.g., AAPL)"
                    value={customSymbol}
                    onChange={(e) => setCustomSymbol(e.target.value)}
                    className="pl-10 pr-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isAnalyzing}
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isAnalyzing || !customSymbol.trim()}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Analyze
                </Button>
              </form>

              <Button
                size="lg"
                onClick={() => handleAnalysis(selectedSymbol)}
                disabled={isAnalyzing}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Analyzing {selectedSymbol}...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-5 w-5" />
                    Get AI Analysis for {selectedSymbol}
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* Strategy Scanner Section */}
      {activeTab === 'strategies' && (
        <section className="py-12 px-4">
          <div className="container mx-auto">
            <StrategyScanner autoScan={true} accountSize={100000} />
          </div>
        </section>
      )}

      {/* Basic Scanner Section */}
      {activeTab === 'scanner' && (
        <section className="py-12 px-4">
          <div className="container mx-auto">
            <SwingScanner autoScan={false} />
          </div>
        </section>
      )}

      {/* Error Display */}
      {error && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <Card className="bg-red-900/20 border-red-500/50">
              <CardContent className="p-6">
                <p className="text-red-300 text-center">{error}</p>
              </CardContent>
            </Card>
          </div>
        </section>
      )}

      {/* Analysis Results */}
      {activeTab === 'individual' && (stockData || analysis) && (
        <section className="py-12 px-4">
          <div className="container mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

              {/* Stock Quote Card */}
              {stockData && (
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <BarChart3 className="mr-2 h-5 w-5 text-blue-400" />
                      {stockData.symbol} Quote
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-300">Price:</span>
                        <span className="text-white font-semibold">{formatCurrency(stockData.price)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Change:</span>
                        <span className={stockData.change >= 0 ? "text-green-400" : "text-red-400"}>
                          {formatCurrency(stockData.change)} ({formatPercentage(stockData.changePercent)})
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Volume:</span>
                        <span className="text-white">{stockData.volume.toLocaleString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Trading Levels Card */}
              {analysis && (
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Target className="mr-2 h-5 w-5 text-green-400" />
                      Trading Levels
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-300">Entry:</span>
                        <span className="text-white font-semibold">{formatCurrency(analysis.entryPrice)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Stop Loss:</span>
                        <span className="text-red-400">{formatCurrency(analysis.stopLoss)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Take Profit:</span>
                        <span className="text-green-400">{formatCurrency(analysis.takeProfit)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Risk/Reward:</span>
                        <span className="text-blue-400 font-semibold">{analysis.riskRewardRatio.toFixed(2)}:1</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Analysis Summary Card */}
              {analysis && (
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <Brain className="mr-2 h-5 w-5 text-purple-400" />
                      AI Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-300">Trend:</span>
                        <span className={`font-semibold ${
                          analysis.trend === 'BULLISH' ? 'text-green-400' :
                          analysis.trend === 'BEARISH' ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {analysis.trend}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Confidence:</span>
                        <span className="text-white font-semibold">{analysis.confidence.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Recommendation:</span>
                        <span className={`font-semibold ${
                          analysis.recommendation.includes('BUY') ? 'text-green-400' :
                          analysis.recommendation.includes('SELL') ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {analysis.recommendation.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Technical Indicators */}
            {analysis && (
              <Card className="mt-6 bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <TrendingUp className="mr-2 h-5 w-5 text-orange-400" />
                    Technical Indicators
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {analysis.indicators.map((indicator, index) => (
                      <div key={index} className="p-4 bg-slate-700/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-white font-medium">{indicator.name}</h4>
                          <span className={`px-2 py-1 rounded text-xs font-semibold ${
                            indicator.signal === 'BUY' ? 'bg-green-500/20 text-green-400' :
                            indicator.signal === 'SELL' ? 'bg-red-500/20 text-red-400' :
                            'bg-yellow-500/20 text-yellow-400'
                          }`}>
                            {indicator.signal}
                          </span>
                        </div>
                        <p className="text-slate-300 text-sm">{indicator.description}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Support and Resistance Levels */}
            {analysis && (analysis.supportLevels.length > 0 || analysis.resistanceLevels.length > 0) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                {analysis.supportLevels.length > 0 && (
                  <Card className="bg-slate-800/50 border-slate-700">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center">
                        <Shield className="mr-2 h-5 w-5 text-green-400" />
                        Support Levels
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {analysis.supportLevels.map((level, index) => (
                          <div key={index} className="flex justify-between">
                            <span className="text-slate-300">Support {index + 1}:</span>
                            <span className="text-green-400 font-semibold">{formatCurrency(level)}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {analysis.resistanceLevels.length > 0 && (
                  <Card className="bg-slate-800/50 border-slate-700">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center">
                        <Shield className="mr-2 h-5 w-5 text-red-400" />
                        Resistance Levels
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {analysis.resistanceLevels.map((level, index) => (
                          <div key={index} className="flex justify-between">
                            <span className="text-slate-300">Resistance {index + 1}:</span>
                            <span className="text-red-400 font-semibold">{formatCurrency(level)}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </section>
      )}
    </div>
  )
}
