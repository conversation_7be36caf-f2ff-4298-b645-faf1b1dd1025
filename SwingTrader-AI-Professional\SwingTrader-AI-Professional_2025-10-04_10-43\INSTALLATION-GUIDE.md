# 📋 SwingTrader AI - Complete Installation Guide

## 🎯 Overview

This guide will walk you through the complete installation process for SwingTrader AI, from system preparation to first trade analysis. The entire process typically takes 10-15 minutes.

---

## 🖥️ System Requirements

### **Minimum Requirements**
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux Ubuntu 18.04+
- **RAM**: 4GB (8GB recommended for optimal performance)
- **Storage**: 2GB free disk space
- **Internet**: Stable broadband connection (required for real-time data)
- **Browser**: Chrome, Firefox, Safari, or Edge (latest versions)

### **Recommended Specifications**
- **OS**: Windows 11 or macOS 12+
- **RAM**: 8GB or more
- **CPU**: Multi-core processor (Intel i5/AMD Ryzen 5 or better)
- **Display**: 1920x1080 resolution or higher
- **Internet**: High-speed connection (25+ Mbps) for optimal real-time data

---

## 🚀 Installation Process

### **Step 1: Download & Extract**

1. **Download** the SwingTrader AI package
2. **Extract** the ZIP file to your desired location
   - Recommended: `C:\SwingTrader-AI\` (Windows) or `~/SwingTrader-AI/` (Mac/Linux)
3. **Navigate** to the extracted folder

### **Step 2: Run the Installer**

#### **Windows Installation**
1. **Right-click** on `install-windows.bat`
2. **Select** "Run as administrator" (recommended)
3. **Follow** the on-screen prompts
4. **Wait** for automatic installation to complete

#### **Mac/Linux Installation**
1. **Open Terminal** in the project folder
2. **Run**: `chmod +x install-mac-linux.sh`
3. **Execute**: `./install-mac-linux.sh`
4. **Follow** the prompts

### **Step 3: API Key Configuration**

The installer will automatically open the configuration file. You need to add your API keys:

#### **Required APIs**

**🔹 Polygon.io (Market Data)**
1. Visit: https://polygon.io/
2. Create free account
3. Verify email address
4. Go to Dashboard → API Keys
5. Copy your API key
6. Paste in `.env.local` file

**🔹 OpenAI (AI Analysis)**
1. Visit: https://platform.openai.com/
2. Create account
3. Add billing method (required)
4. Go to API Keys section
5. Create new secret key
6. Copy and paste in `.env.local` file

#### **Optional APIs**

**🔹 Financial Modeling Prep (Additional Data)**
1. Visit: https://financialmodelingprep.com/
2. Create free account
3. Get API key from dashboard
4. Add to `.env.local` file

**🔹 Alpaca (Paper Trading)**
1. Visit: https://alpaca.markets/
2. Create account
3. Go to Paper Trading → API Keys
4. Generate key pair
5. Add both keys to `.env.local` file

### **Step 4: Launch Application**

After installation completes:

1. **Double-click**: `🚀 Start SwingTrader AI.bat` (Windows)
2. **Or run**: `./start-swingtrader.sh` (Mac/Linux)
3. **Wait** for server to start (30-60 seconds)
4. **Browser opens** automatically to http://localhost:3000

---

## 🔧 Configuration Details

### **Environment File Structure**

Your `.env.local` file should look like this:

```env
# Market Data (Required)
POLYGON_API_KEY=your_actual_polygon_key_here

# AI Analysis (Required)
OPENAI_API_KEY=your_actual_openai_key_here

# Additional Data (Optional)
FMP_API_KEY=your_actual_fmp_key_here

# Paper Trading (Optional)
ALPACA_API_KEY=your_actual_alpaca_key_here
ALPACA_SECRET_KEY=your_actual_alpaca_secret_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Application Settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

### **Security Best Practices**

- ✅ **Never share** your `.env.local` file
- ✅ **Keep API keys** private and secure
- ✅ **Use paper trading** before live trading
- ✅ **Regularly rotate** API keys
- ✅ **Monitor usage** in provider dashboards

---

## 🎯 First-Time Setup

### **1. Verify Installation**

After launching, you should see:
- SwingTrader AI dashboard
- Stock scanning cards
- Navigation menu
- No error messages

### **2. Test API Connections**

1. **Check market data**: Stock prices should load
2. **Test AI analysis**: Click "Analyze with AI" on any stock
3. **Verify paper trading**: Try placing a test trade

### **3. Customize Settings**

1. **Review watchlist**: Default 65+ stocks included
2. **Adjust risk parameters**: Set your preferred position sizes
3. **Configure alerts**: Set up notifications for opportunities

---

## 🔄 Updates & Maintenance

### **Automatic Updates**
- **Windows**: Double-click `🔄 Update SwingTrader AI.bat`
- **Mac/Linux**: Run `./update.sh`

### **Manual Updates**
```bash
npm install
npm run build
```

### **Backup Configuration**
- **Copy** your `.env.local` file before updates
- **Save** any custom watchlists or settings

---

## 🆘 Troubleshooting

### **Installation Issues**

**❌ "Node.js not found"**
- Download from https://nodejs.org/ (LTS version)
- Install with default settings
- Restart computer
- Run installer again

**❌ "Permission denied"**
- Run installer as Administrator (Windows)
- Use `sudo` if needed (Mac/Linux)
- Check antivirus isn't blocking installation

**❌ "Network error during installation"**
- Check internet connection
- Temporarily disable firewall/antivirus
- Try using mobile hotspot
- Run: `npm cache clean --force`

### **Runtime Issues**

**❌ "API key invalid"**
- Double-check key in `.env.local`
- Ensure no extra spaces or quotes
- Verify key is active in provider dashboard
- Try regenerating the key

**❌ "Port 3000 in use"**
- Close other applications using port 3000
- Or change port in `package.json`
- Restart computer if needed

**❌ "Build failed"**
- Delete `node_modules` folder
- Run `npm install` again
- Check Node.js version (should be 18+)

### **Performance Issues**

**❌ "Slow loading"**
- Check internet connection speed
- Verify API rate limits aren't exceeded
- Consider upgrading to paid API tiers
- Close unnecessary browser tabs

---

## 📞 Getting Help

### **Self-Help Resources**
1. Check this installation guide
2. Review the troubleshooting section
3. Verify all API keys are correct
4. Test with minimal configuration first

### **Common Solutions**
- **Restart** the application
- **Clear browser cache**
- **Update** to latest version
- **Check** API provider status pages

---

## ✅ Installation Checklist

Before considering installation complete, verify:

- [ ] Node.js installed and working
- [ ] All dependencies installed successfully
- [ ] API keys configured in `.env.local`
- [ ] Application starts without errors
- [ ] Market data loads correctly
- [ ] AI analysis works
- [ ] Paper trading functions (if configured)
- [ ] Browser opens to correct URL
- [ ] No console errors visible

---

## 🎉 You're Ready to Trade!

Once installation is complete, you'll have access to:

- **Real-time market scanning** of 65+ stocks
- **AI-powered trade analysis** with entry/exit points
- **Professional trading interface** with risk management
- **Paper trading system** for practice
- **Performance tracking** and analytics

**Welcome to professional swing trading with AI assistance!**
