'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Zap, Target, Clock, AlertTriangle, TrendingUp, Moon, BarChart3, Briefcase, Brain } from 'lucide-react'
import { EnhancedScanResult, StrategyScanSummary } from '@/lib/enhancedSwingScanner'
import { PaperTrade } from '@/types/paperTrading'
import { TradingCard } from '@/components/TradingCard'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface StrategyScannerProps {
  autoScan?: boolean
  accountSize?: number
}

export function StrategyScanner({ autoScan = false, accountSize = 100000 }: StrategyScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResults, setScanResults] = useState<StrategyScanSummary | null>(null)
  const [selectedStrategy, setSelectedStrategy] = useState<'both' | 'overnight' | 'breakout'>('both')
  const [error, setError] = useState<string | null>(null)
  const [userAccountSize, setUserAccountSize] = useState(accountSize)
  const [paperTrades, setPaperTrades] = useState<PaperTrade[]>([])
  const [showPaperTrades, setShowPaperTrades] = useState(false)

  // Auto-scan on component mount if enabled
  useEffect(() => {
    if (autoScan) {
      handleStrategyScan('quick')
    }
  }, [autoScan])

  const handleStrategyScan = async (scanType: 'quick' | 'full' | 'test') => {
    setIsScanning(true)
    setError(null)
    setScanResults(null)

    try {
      let response
      if (scanType === 'test') {
        response = await fetch('/api/scanner/test')
      } else {
        response = await fetch(
          `/api/scanner/strategies?type=${scanType}&accountSize=${userAccountSize}&limit=20`
        )
      }

      if (!response.ok) throw new Error('Failed to fetch strategy scan results')

      const data = await response.json()
      console.log('📊 API Response:', data) // Debug logging
      console.log('📊 topSetups:', data?.topSetups) // Debug logging
      console.log('📊 Setting scan results...') // Debug logging
      setScanResults(data)
      console.log('📊 Scan results set!') // Debug logging
    } catch (err) {
      setError('Failed to perform strategy scan. Please try again.')
      console.error('Strategy scan error:', err)
    } finally {
      setIsScanning(false)
    }
  }

  const getStrategyIcon = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return <Moon className="h-4 w-4 text-purple-400" />
      case 'technical_breakout':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      default:
        return <BarChart3 className="h-4 w-4 text-blue-400" />
    }
  }

  const getStrategyName = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return 'Overnight Momentum'
      case 'technical_breakout':
        return 'Technical Breakout'
      default:
        return 'Mixed Strategy'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'overnight_momentum':
        return 'bg-purple-500/20 text-purple-400'
      case 'technical_breakout':
        return 'bg-green-500/20 text-green-400'
      default:
        return 'bg-blue-500/20 text-blue-400'
    }
  }

  const filteredResults = scanResults?.topSetups?.filter(result => {
    if (selectedStrategy === 'both') return true
    if (selectedStrategy === 'overnight') return result.overnightSetup
    if (selectedStrategy === 'breakout') return result.breakoutSetup
    return true
  }) || []

  const handlePaperTradeExecuted = (trade: PaperTrade) => {
    setPaperTrades(prev => [...prev, trade])
  }

  return (
    <div className="space-y-6">
      {/* Strategy Scanner Controls */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Zap className="mr-2 h-5 w-5 text-yellow-400" />
            Professional Swing Trading Strategies
          </CardTitle>
          <CardDescription className="text-slate-300">
            Automated scanner implementing proven swing trading methodologies with precise entry/exit rules
            <br />
            <span className="text-xs text-blue-400">
              🚀 New: AI & Tech Giants scan - 60+ AI, tech, fintech, and high-growth stocks
            </span>
            <br />
            <span className="text-xs text-yellow-400">
              Status: {isScanning ? 'Scanning...' : scanResults ? `Found ${scanResults.totalScanned} stocks, ${scanResults.overnightSetups} setups` : 'Ready to scan'}
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Account Size Input */}
          <div className="mb-4">
            <label className="block text-sm text-slate-300 mb-2">Account Size (for position sizing)</label>
            <input
              type="number"
              value={userAccountSize}
              onChange={(e) => setUserAccountSize(parseInt(e.target.value) || 100000)}
              className="w-32 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white text-sm"
              min="10000"
              step="10000"
              disabled={isScanning}
            />
          </div>

          {/* Strategy Filter */}
          <div className="mb-4">
            <label className="block text-sm text-slate-300 mb-2">Strategy Filter</label>
            <div className="flex gap-2">
              <Button
                variant={selectedStrategy === 'both' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('both')}
                className={selectedStrategy === 'both' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}
              >
                All Strategies
              </Button>
              <Button
                variant={selectedStrategy === 'overnight' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('overnight')}
                className={selectedStrategy === 'overnight' ? 'bg-purple-600' : 'border-slate-600 text-slate-300'}
              >
                <Moon className="mr-1 h-3 w-3" />
                Overnight
              </Button>
              <Button
                variant={selectedStrategy === 'breakout' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStrategy('breakout')}
                className={selectedStrategy === 'breakout' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}
              >
                <TrendingUp className="mr-1 h-3 w-3" />
                Breakout
              </Button>
            </div>
          </div>

          {/* Scan Buttons */}
          <div className="flex flex-wrap gap-4 mb-4">
            <Button
              onClick={() => handleStrategyScan('quick')}
              disabled={isScanning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Zap className="mr-2 h-4 w-4" />
              )}
              Quick Strategy Scan
            </Button>
            
            <Button
              onClick={() => handleStrategyScan('full')}
              disabled={isScanning}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Target className="mr-2 h-4 w-4" />
              )}
              Full Strategy Scan
            </Button>

            <Button
              onClick={() => handleStrategyScan('ai-tech')}
              disabled={isScanning}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
            >
              {isScanning ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Brain className="mr-2 h-4 w-4" />
              )}
              AI & Tech Giants
            </Button>
          </div>

          {/* Market Conditions */}
          {scanResults?.marketConditions && (
            <div className="p-3 bg-slate-700/50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-300">Market Conditions:</span>
                <div className="flex items-center gap-4">
                  <span className="text-white">{scanResults.marketConditions.timeOfDay}</span>
                  <Badge className={scanResults.marketConditions.isOptimalScanTime 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-yellow-500/20 text-yellow-400'
                  }>
                    {scanResults.marketConditions.isOptimalScanTime ? 'Optimal Scan Time' : 'Outside Optimal Hours'}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {isScanning && (
            <div className="text-center py-4">
              <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-400" />
              <p className="text-slate-300 mt-2">
                Analyzing stocks for professional swing trading setups...
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="bg-red-900/20 border-red-500/50">
          <CardContent className="p-6">
            <p className="text-red-300 text-center">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Paper Trades Summary */}
      {paperTrades.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center justify-between">
              <div className="flex items-center">
                <Briefcase className="mr-2 h-5 w-5 text-green-400" />
                Paper Trading Portfolio
              </div>
              <Button
                onClick={() => setShowPaperTrades(!showPaperTrades)}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                {showPaperTrades ? 'Hide' : 'Show'} ({paperTrades.length})
              </Button>
            </CardTitle>
          </CardHeader>
          {showPaperTrades && (
            <CardContent>
              <div className="space-y-3">
                {paperTrades.map((trade, index) => (
                  <div key={trade.id} className="p-3 bg-slate-700/50 rounded border border-slate-600">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <span className="font-semibold text-white">{trade.symbol}</span>
                        <Badge className="ml-2 bg-blue-500/20 text-blue-400">
                          {trade.strategy.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="text-sm text-slate-300">
                        {new Date(trade.entryTime).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div>
                        <span className="text-slate-300">Position: </span>
                        <span className="text-white">{trade.positionSize} shares</span>
                      </div>
                      <div>
                        <span className="text-slate-300">Entry: </span>
                        <span className="text-white">{formatCurrency(trade.entryPrice)}</span>
                      </div>
                      <div>
                        <span className="text-slate-300">Risk: </span>
                        <span className="text-red-400">{formatCurrency(trade.riskAmount)}</span>
                      </div>
                      <div>
                        <span className="text-slate-300">Status: </span>
                        <Badge className={trade.status === 'open' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}>
                          {trade.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Strategy Scan Results */}
      {scanResults && (
        <div className="space-y-6">
          {console.log('📊 Rendering results:', scanResults)}
          {/* Scan Summary */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Strategy Scan Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{scanResults.totalScanned || 0}</div>
                  <div className="text-sm text-slate-300">Stocks Scanned</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{scanResults.overnightSetups || 0}</div>
                  <div className="text-sm text-slate-300">Overnight Setups</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{scanResults.breakoutSetups || 0}</div>
                  <div className="text-sm text-slate-300">Breakout Setups</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{scanResults.bothStrategies || 0}</div>
                  <div className="text-sm text-slate-300">Both Strategies</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Trading Cards */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white flex items-center">
                {scanResults?.category === 'AI & Tech Giants' ? (
                  <Brain className="mr-2 h-5 w-5 text-purple-400" />
                ) : (
                  <Target className="mr-2 h-5 w-5 text-green-400" />
                )}
                {scanResults?.category === 'AI & Tech Giants' ? 'AI & Tech Giants' : 'Professional Trading Setups'} ({filteredResults.length})
              </h2>
              {paperTrades.length > 0 && (
                <Badge className="bg-green-500/20 text-green-400">
                  {paperTrades.length} Paper Trades Active
                </Badge>
              )}
            </div>

            <div className="space-y-4">
              {filteredResults.map((result) => (
                <TradingCard
                  key={result.symbol}
                  result={result}
                  accountSize={userAccountSize}
                  onPaperTradeExecuted={handlePaperTradeExecuted}
                />
              ))}
            </div>
          </div>


        </div>
      )}
    </div>
  )
}
