export interface PaperTrade {
  id: string
  symbol: string
  strategy: 'overnight_momentum' | 'technical_breakout'
  entryPrice: number
  stopLoss: number
  targets: number[]
  positionSize: number
  accountSize: number
  riskAmount: number
  riskPercentage: number
  entryTime: string
  status: 'open' | 'closed' | 'stopped'
  currentPrice?: number
  unrealizedPnL?: number
  notes?: string
}

export interface AIAnalysis {
  symbol: string
  setupExplanation: string
  catalysts?: string[]  // Optional - only in FULL mode
  riskAssessment?: string  // Optional - only in FULL mode
  keyLevels?: string[]  // Optional - only in FULL mode
  timeframe: string
  confidence: number
  lastUpdated?: string  // Optional - only in FULL mode
  // SUMMARY mode specific fields
  entry?: number
  stop?: number
  target?: number
}

export interface TradingCardState {
  isExecuting: boolean
  isAnalyzing: boolean
  paperTrade?: PaperTrade
  aiAnalysis?: AIAnalysis
  showAnalysis: boolean
}
