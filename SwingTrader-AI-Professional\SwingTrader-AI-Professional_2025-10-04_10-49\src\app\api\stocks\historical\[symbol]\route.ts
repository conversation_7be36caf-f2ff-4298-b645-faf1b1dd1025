import { NextRequest, NextResponse } from 'next/server'
import { PolygonAPI } from '@/lib/polygon'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params
    const { searchParams } = new URL(request.url)
    
    const timespan = searchParams.get('timespan') as 'minute' | 'hour' | 'day' | 'week' | 'month' || 'day'
    const multiplier = parseInt(searchParams.get('multiplier') || '1')
    const from = searchParams.get('from')
    const to = searchParams.get('to')
    
    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    if (!from || !to) {
      return NextResponse.json(
        { error: 'From and to date parameters are required' },
        { status: 400 }
      )
    }

    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
    const historicalData = await polygonAPI.getHistoricalData(
      symbol.toUpperCase(),
      timespan,
      multiplier,
      from,
      to
    )
    
    return NextResponse.json(historicalData)
  } catch (error) {
    console.error('Error in historical data API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch historical data' },
      { status: 500 }
    )
  }
}
