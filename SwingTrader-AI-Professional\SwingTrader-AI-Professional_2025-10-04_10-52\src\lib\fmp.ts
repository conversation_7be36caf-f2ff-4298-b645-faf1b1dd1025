import axios from 'axios'
import { StockData } from '@/types/trading'

const FMP_BASE_URL = 'https://financialmodelingprep.com/api'
const API_KEY = process.env.FMP_API_KEY || 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'

export class FMPAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || 'K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7'
    console.log('🔑 FMP API Key prefix:', this.apiKey.substring(0, 15))
    console.log('🔑 Source:', process.env.FMP_API_KEY ? 'Environment Variable' : 'Hardcoded Fallback')
  }

  // Get real-time stock quote with rate limiting
  async getStockQuote(symbol: string): Promise<StockData> {
    try {
      // Add delay to prevent rate limiting
      await this.delay(100) // 100ms delay between requests

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/quote/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          },
          timeout: 10000 // 10 second timeout
        }
      )

      const data = response.data[0]
      if (!data) {
        throw new Error(`No data found for symbol ${symbol}`)
      }

      return {
        symbol: data.symbol,
        name: data.name || data.symbol,
        price: data.price,
        change: data.change,
        changePercent: data.changesPercentage,
        volume: data.volume,
        marketCap: data.marketCap,
        pe: data.pe,
        dividend: undefined // Will be fetched separately if needed
      }
    } catch (error: any) {
      console.error('Error fetching FMP stock quote:', error)

      // Handle rate limiting with exponential backoff
      if (error.response?.status === 429) {
        console.warn(`⚠️ Rate limited for ${symbol}, implementing backoff...`)

        // Try multiple retries with increasing delays
        for (let attempt = 1; attempt <= 3; attempt++) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000) // Max 10 seconds
          console.log(`Retry attempt ${attempt} for ${symbol} after ${delay}ms...`)
          await this.delay(delay)

          try {
            const retryResponse = await axios.get(
              `${FMP_BASE_URL}/v3/quote/${symbol}`,
              {
                params: {
                  apikey: this.apiKey
                },
                timeout: 15000
              }
            )
            const retryData = retryResponse.data[0]
            if (retryData) {
              console.log(`✅ Successfully retrieved ${symbol} on attempt ${attempt}`)
              return {
                symbol: retryData.symbol,
                name: retryData.name || retryData.symbol,
                price: retryData.price,
                change: retryData.change,
                changePercent: retryData.changesPercentage,
                volume: retryData.volume,
                marketCap: retryData.marketCap,
                pe: retryData.pe,
                dividend: undefined
              }
            }
          } catch (retryError: any) {
            if (retryError.response?.status !== 429) {
              // If it's not a rate limit error, break out of retry loop
              break
            }
            console.warn(`Retry ${attempt} failed for ${symbol}, continuing...`)
          }
        }
        console.error(`❌ All retries exhausted for ${symbol}`)
      }

      throw new Error(`Failed to fetch quote for ${symbol}`)
    }
  }

  // Helper method for delays
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Get company profile
  async getCompanyProfile(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/profile/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0]
    } catch (error) {
      console.error('Error fetching company profile:', error)
      return null
    }
  }

  // Get financial ratios
  async getFinancialRatios(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/ratios/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0] // Most recent ratios
    } catch (error) {
      console.error('Error fetching financial ratios:', error)
      return null
    }
  }

  // Get key metrics
  async getKeyMetrics(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/key-metrics/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data[0] // Most recent metrics
    } catch (error) {
      console.error('Error fetching key metrics:', error)
      return null
    }
  }

  // Get analyst recommendations
  async getAnalystRecommendations(symbol: string) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/analyst-stock-recommendations/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching analyst recommendations:', error)
      return []
    }
  }

  // Get earnings calendar
  async getEarningsCalendar(from?: string, to?: string) {
    try {
      const params: any = {
        apikey: this.apiKey
      }

      if (from) params.from = from
      if (to) params.to = to

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/earning_calendar`,
        { params }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching earnings calendar:', error)
      return []
    }
  }

  // Get economic calendar
  async getEconomicCalendar(from?: string, to?: string) {
    try {
      const params: any = {
        apikey: this.apiKey
      }

      if (from) params.from = from
      if (to) params.to = to

      const response = await axios.get(
        `${FMP_BASE_URL}/v3/economic_calendar`,
        { params }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching economic calendar:', error)
      return []
    }
  }

  // Search for stocks
  async searchStocks(query: string, limit: number = 10) {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/search`,
        {
          params: {
            query,
            limit,
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error searching stocks:', error)
      return []
    }
  }

  // Get sector performance
  async getSectorPerformance() {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/sector-performance`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching sector performance:', error)
      return []
    }
  }

  // Get market gainers/losers
  async getMarketMovers(type: 'gainers' | 'losers' | 'actives') {
    try {
      const response = await axios.get(
        `${FMP_BASE_URL}/v3/stock_market/${type}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error(`Error fetching market ${type}:`, error)
      return []
    }
  }
}

// Create a singleton instance
export const fmpAPI = new FMPAPI()
