import { NextRequest, NextResponse } from 'next/server'
import { enhancedSwingScanner } from '@/lib/enhancedSwingScanner'
import { AI_TECH_GIANTS_SYMBOLS, AI_TECH_GIANTS } from '@/data/watchlist'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    console.log('🚀 Starting AI & Tech Giants comprehensive scan...')
    console.log(`📊 Scanning ${AI_TECH_GIANTS_SYMBOLS.length} AI, Tech, and Growth stocks`)
    
    // Set account size for position sizing
    const scanner = new (require('@/lib/enhancedSwingScanner').EnhancedSwingScanner)(accountSize)
    
    // Scan AI & Tech Giants with moderate concurrency for quality data
    const summary = await scanner.scanWithStrategies(AI_TECH_GIANTS_SYMBOLS, 4)
    
    // Add category information to results
    const enhancedSummary = {
      ...summary,
      category: 'AI & Tech Giants',
      description: 'Comprehensive scan of 60+ AI, technology, fintech, and high-growth stocks',
      stockDetails: AI_TECH_GIANTS,
      topSetups: summary.topSetups?.map(setup => ({
        ...setup,
        category: 'AI & Tech Giants',
        aiExposure: getAIExposureLevel(setup.symbol),
        marketCapCategory: getMarketCapCategory(setup.symbol)
      })) || []
    }
    
    // Limit results if requested
    const limitedSummary = {
      ...enhancedSummary,
      topSetups: enhancedSummary.topSetups.slice(0, limit)
    }

    console.log(`✅ AI & Tech Giants scan completed: ${limitedSummary.topSetups.length} setups found`)

    return NextResponse.json(limitedSummary)
  } catch (error) {
    console.error('❌ Error in AI & Tech Giants scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform AI & Tech Giants scan' },
      { status: 500 }
    )
  }
}

// Helper function to determine AI exposure level
function getAIExposureLevel(symbol: string): 'Primary' | 'Secondary' | 'Quantum' | 'Adjacent' {
  const primaryAI = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'ORCL', 'NFLX', 'AMD', 'TSM']
  const secondaryAI = ['ANET', 'MU', 'LRCX', 'DELL', 'WDC', 'VRT', 'ZS', 'NET', 'SHOP']
  const quantumAI = ['IONQ', 'RGTI', 'SOUN', 'QBTS']

  if (primaryAI.includes(symbol)) return 'Primary'
  if (secondaryAI.includes(symbol)) return 'Secondary'
  if (quantumAI.includes(symbol)) return 'Quantum'
  return 'Adjacent'
}

// Helper function to get market cap category
function getMarketCapCategory(symbol: string): string {
  const stock = AI_TECH_GIANTS.find(s => s.symbol === symbol)
  return stock?.marketCap === 'large' ? '100B+' : 
         stock?.marketCap === 'mid' ? '10B-100B' : 
         '1B-10B'
}
