@echo off
setlocal enabledelayedexpansion
title SwingTrader AI - Deployment Package Creator

REM Set colors for better visual experience
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "GREEN=%ESC%[92m"
set "RED=%ESC%[91m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

cls
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                    📦 SwingTrader AI Package Creator                         ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                     Create Professional Deployment Package                  ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %WHITE%This script creates a complete deployment package for distribution%RESET%
echo.

REM Set package name with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "TIMESTAMP=%YYYY%-%MM%-%DD%_%HH%-%Min%"
set "PACKAGE_NAME=SwingTrader-AI-Professional_%TIMESTAMP%"

echo %BLUE%[1/8] Preparing package: %PACKAGE_NAME%%RESET%
echo.

REM Create package directory
if exist "%PACKAGE_NAME%" (
    echo %YELLOW%Removing existing package directory...%RESET%
    rmdir /s /q "%PACKAGE_NAME%"
)
mkdir "%PACKAGE_NAME%"

echo %GREEN%✅ Package directory created%RESET%
echo.

REM Copy essential application files
echo %BLUE%[2/8] Copying application files...%RESET%

REM Core application files
copy "package.json" "%PACKAGE_NAME%\" >nul
copy "package-lock.json" "%PACKAGE_NAME%\" >nul
copy "next.config.ts" "%PACKAGE_NAME%\" >nul
copy "tsconfig.json" "%PACKAGE_NAME%\" >nul
copy "postcss.config.mjs" "%PACKAGE_NAME%\" >nul
copy "eslint.config.mjs" "%PACKAGE_NAME%\" >nul
copy "next-env.d.ts" "%PACKAGE_NAME%\" >nul

REM Copy source code
xcopy "src" "%PACKAGE_NAME%\src\" /E /I /Q >nul
xcopy "public" "%PACKAGE_NAME%\public\" /E /I /Q >nul

echo %GREEN%✅ Application files copied%RESET%
echo.

REM Copy installation and setup files
echo %BLUE%[3/8] Copying installation files...%RESET%

copy "install-windows.bat" "%PACKAGE_NAME%\" >nul
copy "install-mac-linux.sh" "%PACKAGE_NAME%\" >nul
copy "nodejs-installer.bat" "%PACKAGE_NAME%\" >nul
copy "🚀 Start SwingTrader AI.bat" "%PACKAGE_NAME%\" >nul 2>nul
copy "🔄 Update SwingTrader AI.bat" "%PACKAGE_NAME%\" >nul 2>nul

REM Create startup scripts if they don't exist
if not exist "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat" (
    echo @echo off > "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
    echo title SwingTrader AI - Professional Trading Platform >> "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
    echo echo Starting SwingTrader AI... >> "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
    echo timeout /t 3 ^>nul >> "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
    echo start http://localhost:3000 >> "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
    echo call npm start >> "%PACKAGE_NAME%\🚀 Start SwingTrader AI.bat"
)

if not exist "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat" (
    echo @echo off > "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo title SwingTrader AI - Update >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo echo Updating SwingTrader AI... >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo call npm install >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo call npm run build >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo echo Update complete! >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
    echo pause >> "%PACKAGE_NAME%\🔄 Update SwingTrader AI.bat"
)

echo %GREEN%✅ Installation files copied%RESET%
echo.

REM Copy documentation
echo %BLUE%[4/8] Copying documentation...%RESET%

copy "README.md" "%PACKAGE_NAME%\" >nul
copy "INSTALLATION-GUIDE.md" "%PACKAGE_NAME%\" >nul
copy "USER-MANUAL.md" "%PACKAGE_NAME%\" >nul
copy ".env.local.example" "%PACKAGE_NAME%\" >nul

REM Copy existing documentation if available
copy "DEPLOYMENT-GUIDE.md" "%PACKAGE_NAME%\" >nul 2>nul
copy "DEPLOYMENT_GUIDE.md" "%PACKAGE_NAME%\" >nul 2>nul

echo %GREEN%✅ Documentation copied%RESET%
echo.

REM Create additional helpful files
echo %BLUE%[5/8] Creating additional files...%RESET%

REM Create a simple README for the package
echo # 🚀 SwingTrader AI - Professional Trading Platform > "%PACKAGE_NAME%\QUICK-START.txt"
echo. >> "%PACKAGE_NAME%\QUICK-START.txt"
echo ## Quick Installation: >> "%PACKAGE_NAME%\QUICK-START.txt"
echo. >> "%PACKAGE_NAME%\QUICK-START.txt"
echo 1. Double-click: install-windows.bat >> "%PACKAGE_NAME%\QUICK-START.txt"
echo 2. Follow the setup wizard >> "%PACKAGE_NAME%\QUICK-START.txt"
echo 3. Add your API keys when prompted >> "%PACKAGE_NAME%\QUICK-START.txt"
echo 4. Application launches automatically >> "%PACKAGE_NAME%\QUICK-START.txt"
echo. >> "%PACKAGE_NAME%\QUICK-START.txt"
echo ## Need Help? >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - Read INSTALLATION-GUIDE.md for detailed instructions >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - Check USER-MANUAL.md for usage instructions >> "%PACKAGE_NAME%\QUICK-START.txt"
echo. >> "%PACKAGE_NAME%\QUICK-START.txt"
echo ## System Requirements: >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - Windows 10/11 >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - 4GB RAM (8GB recommended) >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - Internet connection >> "%PACKAGE_NAME%\QUICK-START.txt"
echo - Node.js (installer will help you get this) >> "%PACKAGE_NAME%\QUICK-START.txt"

REM Create system info script
echo @echo off > "%PACKAGE_NAME%\system-info.bat"
echo title System Information - SwingTrader AI >> "%PACKAGE_NAME%\system-info.bat"
echo echo System Information for SwingTrader AI Support >> "%PACKAGE_NAME%\system-info.bat"
echo echo ============================================== >> "%PACKAGE_NAME%\system-info.bat"
echo echo. >> "%PACKAGE_NAME%\system-info.bat"
echo echo Windows Version: >> "%PACKAGE_NAME%\system-info.bat"
echo ver >> "%PACKAGE_NAME%\system-info.bat"
echo echo. >> "%PACKAGE_NAME%\system-info.bat"
echo echo Node.js Version: >> "%PACKAGE_NAME%\system-info.bat"
echo node --version 2^>nul ^|^| echo Not installed >> "%PACKAGE_NAME%\system-info.bat"
echo echo. >> "%PACKAGE_NAME%\system-info.bat"
echo echo npm Version: >> "%PACKAGE_NAME%\system-info.bat"
echo npm --version 2^>nul ^|^| echo Not installed >> "%PACKAGE_NAME%\system-info.bat"
echo echo. >> "%PACKAGE_NAME%\system-info.bat"
echo pause >> "%PACKAGE_NAME%\system-info.bat"

echo %GREEN%✅ Additional files created%RESET%
echo.

REM Clean up any development files that shouldn't be distributed
echo %BLUE%[6/8] Cleaning up development files...%RESET%

REM Remove any existing node_modules, .next, build artifacts
if exist "%PACKAGE_NAME%\node_modules" rmdir /s /q "%PACKAGE_NAME%\node_modules"
if exist "%PACKAGE_NAME%\.next" rmdir /s /q "%PACKAGE_NAME%\.next"
if exist "%PACKAGE_NAME%\build" rmdir /s /q "%PACKAGE_NAME%\build"
if exist "%PACKAGE_NAME%\dist" rmdir /s /q "%PACKAGE_NAME%\dist"

REM Remove any .env files (users should create their own)
if exist "%PACKAGE_NAME%\.env.local" del "%PACKAGE_NAME%\.env.local"
if exist "%PACKAGE_NAME%\.env" del "%PACKAGE_NAME%\.env"

echo %GREEN%✅ Development files cleaned%RESET%
echo.

REM Create package info file
echo %BLUE%[7/8] Creating package information...%RESET%

echo # SwingTrader AI - Professional Package > "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo. >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo Package Created: %DATE% %TIME% >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo Package Name: %PACKAGE_NAME% >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo Version: 1.0.0 Professional >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo. >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo ## Contents: >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - Complete SwingTrader AI application >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - Automated Windows installer >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - Node.js installation helper >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - Comprehensive documentation >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - User manual and guides >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo - Professional startup scripts >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo. >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo ## Installation: >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo 1. Extract this package to your desired location >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo 2. Double-click install-windows.bat >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo 3. Follow the guided setup process >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo 4. Add your API keys when prompted >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"
echo 5. Start trading with AI assistance! >> "%PACKAGE_NAME%\PACKAGE-INFO.txt"

echo %GREEN%✅ Package information created%RESET%
echo.

REM Create ZIP package
echo %BLUE%[8/8] Creating ZIP package...%RESET%
echo %WHITE%This may take a moment...%RESET%

REM Use PowerShell to create ZIP file
powershell -Command "Compress-Archive -Path '%PACKAGE_NAME%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"

if %errorlevel% neq 0 (
    echo %RED%❌ Failed to create ZIP package%RESET%
    echo %YELLOW%The folder package is still available: %PACKAGE_NAME%%RESET%
) else (
    echo %GREEN%✅ ZIP package created: %PACKAGE_NAME%.zip%RESET%
    
    REM Get file size
    for %%A in ("%PACKAGE_NAME%.zip") do set "FILESIZE=%%~zA"
    set /a FILESIZE_MB=!FILESIZE!/1024/1024
    echo %WHITE%   Package size: !FILESIZE_MB! MB%RESET%
)

echo.

REM Final success message
cls
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%║                        🎉 PACKAGE CREATION COMPLETE! 🎉                     ║%RESET%
echo %CYAN%║                                                                              ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %GREEN%SwingTrader AI deployment package has been created successfully!%RESET%
echo.
echo %WHITE%📦 PACKAGE DETAILS:%RESET%
echo %CYAN%   Folder: %PACKAGE_NAME%%RESET%
echo %CYAN%   ZIP File: %PACKAGE_NAME%.zip%RESET%
if defined FILESIZE_MB echo %CYAN%   Size: !FILESIZE_MB! MB%RESET%
echo.
echo %WHITE%📋 PACKAGE CONTENTS:%RESET%
echo %CYAN%   ✅ Complete SwingTrader AI application%RESET%
echo %CYAN%   ✅ Automated Windows installer%RESET%
echo %CYAN%   ✅ Node.js installation helper%RESET%
echo %CYAN%   ✅ Professional startup scripts%RESET%
echo %CYAN%   ✅ Comprehensive documentation%RESET%
echo %CYAN%   ✅ User manual and guides%RESET%
echo %CYAN%   ✅ Environment template%RESET%
echo.
echo %WHITE%🚀 READY FOR DISTRIBUTION:%RESET%
echo %CYAN%   • Share the ZIP file with users%RESET%
echo %CYAN%   • Recipients just extract and run install-windows.bat%RESET%
echo %CYAN%   • One-click professional installation experience%RESET%
echo.
echo %WHITE%🎯 PROFESSIONAL DEPLOYMENT PACKAGE READY!%RESET%
echo.
pause

exit /b 0
