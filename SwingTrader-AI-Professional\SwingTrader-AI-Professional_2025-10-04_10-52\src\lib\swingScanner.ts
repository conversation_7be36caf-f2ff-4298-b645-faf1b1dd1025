import { SwingTradingAnalyzer } from './swingAnalysis'
import { PolygonAPI } from './polygon'
import { FMPAPI } from './fmp'
import { SwingTradingAnalysis, StockData } from '@/types/trading'
import { format, subDays } from 'date-fns'

export interface ScanResult {
  symbol: string
  name: string
  sector: string
  quote: StockData
  analysis: SwingTradingAnalysis
  score: number
  rank: number
  scanTime: string
}

export interface ScanSummary {
  totalScanned: number
  successfulScans: number
  failedScans: number
  topOpportunities: ScanResult[]
  sectorBreakdown: Record<string, number>
  scanDuration: number
}

export class SwingTradingScanner {
  private fmpAPI: FMPAPI
  private polygonAPI: PolygonAPI

  constructor() {
    this.fmpAPI = new FMPAPI(process.env.FMP_API_KEY)
    this.polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
  }

  // Main scanning function
  async scanStocks(symbols: string[], maxConcurrent: number = 5): Promise<ScanSummary> {
    const startTime = Date.now()
    const results: ScanResult[] = []
    const failed: string[] = []
    const sectorBreakdown: Record<string, number> = {}

    console.log(`Starting scan of ${symbols.length} stocks...`)

    // Process stocks in batches to avoid rate limits
    for (let i = 0; i < symbols.length; i += maxConcurrent) {
      const batch = symbols.slice(i, i + maxConcurrent)
      const batchPromises = batch.map(symbol => this.scanSingleStock(symbol))
      
      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach((result, index) => {
        const symbol = batch[index]
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value)
          const sector = result.value.sector
          sectorBreakdown[sector] = (sectorBreakdown[sector] || 0) + 1
        } else {
          failed.push(symbol)
          console.warn(`Failed to scan ${symbol}:`, result.status === 'rejected' ? result.reason : 'Unknown error')
        }
      })

      // Add delay between batches to respect rate limits
      if (i + maxConcurrent < symbols.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // Sort by score and assign ranks
    results.sort((a, b) => b.score - a.score)
    results.forEach((result, index) => {
      result.rank = index + 1
    })

    const scanDuration = Date.now() - startTime

    return {
      totalScanned: symbols.length,
      successfulScans: results.length,
      failedScans: failed.length,
      topOpportunities: results.slice(0, 20), // Top 20 opportunities
      sectorBreakdown,
      scanDuration
    }
  }

  // Scan a single stock
  private async scanSingleStock(symbol: string): Promise<ScanResult | null> {
    try {
      // Get stock quote and historical data in parallel
      const [quote, historicalData] = await Promise.all([
        this.fmpAPI.getStockQuote(symbol),
        this.getHistoricalData(symbol)
      ])

      if (!historicalData || historicalData.length < 50) {
        throw new Error('Insufficient historical data')
      }

      // Perform swing trading analysis
      const analysis = SwingTradingAnalyzer.analyzeSwingTrade(symbol, historicalData)
      
      // Calculate swing trading score
      const score = this.calculateSwingScore(quote, analysis)

      return {
        symbol,
        name: quote.name,
        sector: this.getSectorForSymbol(symbol),
        quote,
        analysis,
        score,
        rank: 0, // Will be set after sorting
        scanTime: new Date().toISOString()
      }
    } catch (error) {
      console.error(`Error scanning ${symbol}:`, error)
      return null
    }
  }

  // Get historical data with fallback
  private async getHistoricalData(symbol: string) {
    const to = format(new Date(), 'yyyy-MM-dd')
    const from = format(subDays(new Date(), 100), 'yyyy-MM-dd')

    try {
      // Try Polygon first
      return await this.polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)
    } catch (error) {
      console.warn(`Polygon failed for ${symbol}, trying alternative...`)
      // Could add FMP historical data as fallback here
      throw error
    }
  }

  // Calculate swing trading score (0-100)
  private calculateSwingScore(quote: StockData, analysis: SwingTradingAnalysis): number {
    let score = 0

    // Base confidence score (0-40 points)
    score += analysis.confidence * 0.4

    // Risk/Reward ratio bonus (0-20 points)
    const rrRatio = analysis.riskRewardRatio
    if (rrRatio >= 3) score += 20
    else if (rrRatio >= 2) score += 15
    else if (rrRatio >= 1.5) score += 10
    else if (rrRatio >= 1) score += 5

    // Volume factor (0-15 points)
    const volumeIndicator = analysis.indicators.find(ind => ind.name === 'Volume')
    if (volumeIndicator) {
      if (volumeIndicator.signal === 'BUY' && volumeIndicator.value > 1.5) score += 15
      else if (volumeIndicator.signal === 'BUY') score += 10
      else if (volumeIndicator.signal === 'NEUTRAL') score += 5
    }

    // Trend strength bonus (0-15 points)
    if (analysis.trend === 'BULLISH') {
      score += 15
    } else if (analysis.trend === 'BEARISH') {
      score += 10 // Bearish can also be good for short opportunities
    } else {
      score += 5 // Sideways trends can be good for range trading
    }

    // Technical indicator alignment (0-10 points)
    const bullishSignals = analysis.indicators.filter(ind => ind.signal === 'BUY').length
    const bearishSignals = analysis.indicators.filter(ind => ind.signal === 'SELL').length
    const totalSignals = analysis.indicators.length

    if (bullishSignals > bearishSignals) {
      score += (bullishSignals / totalSignals) * 10
    } else if (bearishSignals > bullishSignals) {
      score += (bearishSignals / totalSignals) * 8 // Slightly less for bearish
    }

    // Price momentum bonus/penalty (0-10 points)
    const changePercent = Math.abs(quote.changePercent)
    if (changePercent > 5) score += 10 // High momentum
    else if (changePercent > 2) score += 7
    else if (changePercent > 1) score += 5
    else if (changePercent < 0.5) score -= 5 // Low momentum penalty

    // Recommendation bonus
    switch (analysis.recommendation) {
      case 'STRONG_BUY':
        score += 10
        break
      case 'BUY':
        score += 7
        break
      case 'STRONG_SELL':
        score += 8 // Good for short opportunities
        break
      case 'SELL':
        score += 5
        break
      case 'NO_TRADE':
        score -= 10
        break
    }

    return Math.max(0, Math.min(100, score))
  }

  // Get sector for symbol (simplified mapping)
  private getSectorForSymbol(symbol: string): string {
    const techSymbols = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'TSM', 'ORCL', 'CSCO', 'AMD', 'ASML', 'MU', 'LRCX', 'PLTR', 'APP', 'NET', 'DDOG', 'ZS', 'SHOP', 'SOUN', 'IONQ', 'RGTI', 'RIOT', 'HUT', 'IREN', 'ASTS', 'NBIS']
    const financialSymbols = ['JPM', 'BAC', 'MS', 'SCHW', 'C', 'HOOD', 'SOFI', 'TIGR', 'FUTU']
    const healthcareSymbols = ['JNJ', 'ABBV', 'MRK', 'GILD']
    const industrialSymbols = ['GE', 'CAT', 'BA', 'GEV', 'UAL', 'VRT', 'RKLB']
    const materialsSymbols = ['AEM', 'NEM', 'PAAS', 'BTG', 'HL', 'MP', 'AG']
    const consumerSymbols = ['AMZN', 'DIS', 'SBUX', 'MO', 'DASH', 'GM', 'NCLH', 'CELH', 'LEVI', 'ELF', 'ETSY', 'W']
    const communicationSymbols = ['NFLX', 'RBLX', 'BILI']
    const energySymbols = ['CEG', 'VST', 'CCJ']

    if (techSymbols.includes(symbol)) return 'Technology'
    if (financialSymbols.includes(symbol)) return 'Financial Services'
    if (healthcareSymbols.includes(symbol)) return 'Healthcare'
    if (industrialSymbols.includes(symbol)) return 'Industrial'
    if (materialsSymbols.includes(symbol)) return 'Materials'
    if (consumerSymbols.includes(symbol)) return 'Consumer'
    if (communicationSymbols.includes(symbol)) return 'Communication Services'
    if (energySymbols.includes(symbol)) return 'Energy'
    
    return 'Other'
  }

  // Quick scan of priority stocks
  async quickScan(prioritySymbols: string[]): Promise<ScanResult[]> {
    const summary = await this.scanStocks(prioritySymbols, 8)
    return summary.topOpportunities
  }
}

// Create singleton instance
export const swingScanner = new SwingTradingScanner()
