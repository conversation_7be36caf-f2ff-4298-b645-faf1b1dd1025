#!/bin/bash

echo "========================================"
echo "SwingTrader AI - Mac/Linux Installation"
echo "========================================"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed!"
    echo ""
    echo "Please install Node.js from: https://nodejs.org/"
    echo "Choose the LTS version and run this script again."
    echo ""
    echo "On Mac with Homebrew: brew install node"
    echo "On Ubuntu/Debian: sudo apt install nodejs npm"
    echo "On CentOS/RHEL: sudo yum install nodejs npm"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"
echo ""

# Install dependencies
echo "📦 Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"
echo ""

# Check for environment file
if [ ! -f ".env.local" ]; then
    echo "📝 Creating environment file..."
    cp ".env.local.example" ".env.local"
    echo ""
    echo "⚠️  IMPORTANT: Please edit .env.local with your API keys:"
    echo "   - POLYGON_API_KEY=your_polygon_api_key"
    echo "   - FMP_API_KEY=your_fmp_api_key"
    echo "   - ALPACA_API_KEY=your_alpaca_api_key"
    echo "   - ALPACA_SECRET_KEY=your_alpaca_secret_key"
    echo ""
    echo "Opening .env.local file for editing..."
    
    # Try to open with different editors
    if command -v code &> /dev/null; then
        code .env.local
    elif command -v nano &> /dev/null; then
        nano .env.local
    elif command -v vim &> /dev/null; then
        vim .env.local
    else
        echo "Please edit .env.local manually with your preferred editor"
    fi
    
    echo ""
    read -p "Press Enter after you've added your API keys..."
fi

echo "✅ Environment configured"
echo ""

# Build the application
echo "🔨 Building application..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"
echo ""

# Create startup script
echo "🚀 Creating startup script..."
cat > start-swingtrader.sh << 'EOF'
#!/bin/bash
echo "SwingTrader AI is starting..."
echo "Open your browser to: http://localhost:3000"
echo "Press Ctrl+C to stop the server"
echo ""
npm start
EOF

chmod +x start-swingtrader.sh

echo "✅ Startup script created"
echo ""

echo "🎉 Installation Complete!"
echo ""
echo "To start SwingTrader AI:"
echo "  1. Run: ./start-swingtrader.sh"
echo "  2. Open your browser to http://localhost:3000"
echo ""
echo "To share with others:"
echo "  1. Copy this entire folder"
echo "  2. Run: chmod +x install-mac-linux.sh && ./install-mac-linux.sh"
echo "  3. Add the API keys to .env.local"
echo ""
