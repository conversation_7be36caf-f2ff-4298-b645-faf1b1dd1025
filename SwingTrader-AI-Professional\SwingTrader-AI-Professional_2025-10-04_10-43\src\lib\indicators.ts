import { CandlestickData, TechnicalIndicator } from '@/types/trading'

export class TechnicalIndicators {
  // Simple Moving Average
  static sma(data: number[], period: number): number[] {
    const result: number[] = []
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      result.push(sum / period)
    }
    return result
  }

  // Exponential Moving Average
  static ema(data: number[], period: number): number[] {
    const result: number[] = []
    const multiplier = 2 / (period + 1)
    
    // Start with SMA for first value
    let ema = data.slice(0, period).reduce((a, b) => a + b, 0) / period
    result.push(ema)
    
    for (let i = period; i < data.length; i++) {
      ema = (data[i] * multiplier) + (ema * (1 - multiplier))
      result.push(ema)
    }
    
    return result
  }

  // Relative Strength Index
  static rsi(data: number[], period: number = 14): number[] {
    const gains: number[] = []
    const losses: number[] = []
    
    for (let i = 1; i < data.length; i++) {
      const change = data[i] - data[i - 1]
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }
    
    const avgGains = this.sma(gains, period)
    const avgLosses = this.sma(losses, period)
    
    return avgGains.map((gain, i) => {
      const rs = gain / avgLosses[i]
      return 100 - (100 / (1 + rs))
    })
  }

  // MACD (Moving Average Convergence Divergence)
  static macd(data: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const fastEMA = this.ema(data, fastPeriod)
    const slowEMA = this.ema(data, slowPeriod)
    
    // Align arrays (slowEMA starts later)
    const startIndex = slowPeriod - fastPeriod
    const macdLine = fastEMA.slice(startIndex).map((fast, i) => fast - slowEMA[i])
    
    const signalLine = this.ema(macdLine, signalPeriod)
    const histogram = macdLine.slice(signalPeriod - 1).map((macd, i) => macd - signalLine[i])
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram
    }
  }

  // Bollinger Bands
  static bollingerBands(data: number[], period: number = 20, stdDev: number = 2) {
    const sma = this.sma(data, period)
    const bands = sma.map((avg, i) => {
      const slice = data.slice(i, i + period)
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / period
      const standardDeviation = Math.sqrt(variance)
      
      return {
        upper: avg + (standardDeviation * stdDev),
        middle: avg,
        lower: avg - (standardDeviation * stdDev)
      }
    })
    
    return bands
  }

  // Support and Resistance Levels
  static findSupportResistance(candles: CandlestickData[], lookback: number = 20): { support: number[], resistance: number[] } {
    const highs = candles.map(c => c.high)
    const lows = candles.map(c => c.low)
    
    const resistance: number[] = []
    const support: number[] = []
    
    for (let i = lookback; i < candles.length - lookback; i++) {
      const currentHigh = highs[i]
      const currentLow = lows[i]
      
      // Check if current high is a local maximum
      const isResistance = highs.slice(i - lookback, i).every(h => h <= currentHigh) &&
                          highs.slice(i + 1, i + lookback + 1).every(h => h <= currentHigh)
      
      // Check if current low is a local minimum
      const isSupport = lows.slice(i - lookback, i).every(l => l >= currentLow) &&
                       lows.slice(i + 1, i + lookback + 1).every(l => l >= currentLow)
      
      if (isResistance) resistance.push(currentHigh)
      if (isSupport) support.push(currentLow)
    }
    
    return { support, resistance }
  }

  // Volume analysis
  static volumeAnalysis(candles: CandlestickData[], period: number = 20) {
    const volumes = candles.map(c => c.volume)
    const avgVolume = this.sma(volumes, period)
    const currentVolume = volumes[volumes.length - 1]
    const currentAvgVolume = avgVolume[avgVolume.length - 1]

    return {
      currentVolume,
      averageVolume: currentAvgVolume,
      volumeRatio: currentVolume / currentAvgVolume,
      isHighVolume: currentVolume > currentAvgVolume * 1.5,
      isLowVolume: currentVolume < currentAvgVolume * 0.5
    }
  }

  // Swing Trading Analysis
  static analyzeSwingSetup(candles: CandlestickData[]): TechnicalIndicator[] {
    const closes = candles.map(c => c.close)
    const indicators: TechnicalIndicator[] = []

    // RSI Analysis
    const rsi = this.rsi(closes)
    const currentRSI = rsi[rsi.length - 1]

    let rsiSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let rsiDescription = `RSI: ${currentRSI.toFixed(2)}`

    if (currentRSI < 30) {
      rsiSignal = 'BUY'
      rsiDescription += ' - Oversold condition, potential bounce'
    } else if (currentRSI > 70) {
      rsiSignal = 'SELL'
      rsiDescription += ' - Overbought condition, potential pullback'
    } else {
      rsiDescription += ' - Neutral zone'
    }

    indicators.push({
      name: 'RSI',
      value: currentRSI,
      signal: rsiSignal,
      description: rsiDescription
    })

    // Moving Average Analysis
    const sma20 = this.sma(closes, 20)
    const sma50 = this.sma(closes, 50)
    const currentPrice = closes[closes.length - 1]
    const currentSMA20 = sma20[sma20.length - 1]
    const currentSMA50 = sma50[sma50.length - 1]

    let maSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let maDescription = `Price vs SMA20: ${((currentPrice / currentSMA20 - 1) * 100).toFixed(2)}%`

    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
      maSignal = 'BUY'
      maDescription += ' - Bullish trend'
    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
      maSignal = 'SELL'
      maDescription += ' - Bearish trend'
    } else {
      maDescription += ' - Mixed signals'
    }

    indicators.push({
      name: 'Moving Averages',
      value: (currentPrice / currentSMA20 - 1) * 100,
      signal: maSignal,
      description: maDescription
    })

    // MACD Analysis
    const macdData = this.macd(closes)
    const currentMACD = macdData.macd[macdData.macd.length - 1]
    const currentSignal = macdData.signal[macdData.signal.length - 1]
    const currentHistogram = macdData.histogram[macdData.histogram.length - 1]

    let macdSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let macdDescription = `MACD: ${currentMACD.toFixed(4)}, Signal: ${currentSignal.toFixed(4)}`

    if (currentMACD > currentSignal && currentHistogram > 0) {
      macdSignal = 'BUY'
      macdDescription += ' - Bullish momentum'
    } else if (currentMACD < currentSignal && currentHistogram < 0) {
      macdSignal = 'SELL'
      macdDescription += ' - Bearish momentum'
    } else {
      macdDescription += ' - Momentum shifting'
    }

    indicators.push({
      name: 'MACD',
      value: currentHistogram,
      signal: macdSignal,
      description: macdDescription
    })

    // Volume Analysis
    const volumeData = this.volumeAnalysis(candles)
    let volumeSignal: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL'
    let volumeDescription = `Volume: ${(volumeData.volumeRatio * 100).toFixed(0)}% of average`

    if (volumeData.isHighVolume) {
      volumeSignal = 'BUY'
      volumeDescription += ' - High volume confirms move'
    } else if (volumeData.isLowVolume) {
      volumeSignal = 'SELL'
      volumeDescription += ' - Low volume, weak conviction'
    } else {
      volumeDescription += ' - Normal volume'
    }

    indicators.push({
      name: 'Volume',
      value: volumeData.volumeRatio,
      signal: volumeSignal,
      description: volumeDescription
    })

    return indicators
  }
}
