import { NextRequest, NextResponse } from 'next/server'
import { enhancedSwingScanner } from '@/lib/enhancedSwingScanner'
import { PRIORITY_SYMBOLS, ALL_SYMBOLS, AI_TECH_GIANTS_SYMBOLS } from '@/data/watchlist'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const scanType = searchParams.get('type') || 'quick' // quick, full, ai-tech
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const limit = parseInt(searchParams.get('limit') || '20')

    console.log(`Starting ${scanType} strategy scan...`)

    // Set account size for position sizing
    const scanner = new (require('@/lib/enhancedSwingScanner').EnhancedSwingScanner)(accountSize)

    let summary
    if (scanType === 'ai-tech') {
      // AI & Tech Giants focused scan
      console.log(`🚀 AI & Tech Giants scan: ${AI_TECH_GIANTS_SYMBOLS.length} stocks`)
      summary = await scanner.scanWithStrategies(AI_TECH_GIANTS_SYMBOLS, 4) // Moderate concurrency for quality
      summary.category = 'AI & Tech Giants'
      summary.description = 'Comprehensive scan of 60+ AI, technology, fintech, and high-growth stocks'
    } else if (scanType === 'full') {
      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 1) // Sequential to prevent rate limiting
    } else {
      // Use ALL_SYMBOLS for quick scan too, but with higher concurrency
      summary = await scanner.scanWithStrategies(ALL_SYMBOLS, 3) // Scan all 70+ stocks with moderate concurrency
    }
    
    // Limit results if requested
    const limitedSummary = {
      ...summary,
      topSetups: summary.topSetups?.slice(0, limit) || []
    }

    console.log('📊 API returning summary:', limitedSummary) // Debug logging

    return NextResponse.json(limitedSummary)
  } catch (error) {
    console.error('Error in strategy scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform strategy scan' },
      { status: 500 }
    )
  }
}
