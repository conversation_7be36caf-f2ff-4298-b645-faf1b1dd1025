# SwingTrader AI - Professional Package Creator
Write-Host "SwingTrader AI - Package Creator" -ForegroundColor Cyan
Write-Host "Creating professional deployment package..." -ForegroundColor White
Write-Host ""

# Set package name with timestamp
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
$packageName = "SwingTrader-AI-Professional_$timestamp"

Write-Host "Preparing package: $packageName" -ForegroundColor Blue

# Create package directory
if (Test-Path $packageName) {
    Remove-Item -Path $packageName -Recurse -Force
}
New-Item -ItemType Directory -Path $packageName | Out-Null

# Copy essential files
$coreFiles = @("package.json", "package-lock.json", "next.config.ts", "tsconfig.json", "postcss.config.mjs", "eslint.config.mjs", "next-env.d.ts")
foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $packageName
    }
}

# Copy directories
$sourceDirs = @("src", "public")
foreach ($dir in $sourceDirs) {
    if (Test-Path $dir) {
        Copy-Item $dir -Destination $packageName -Recurse
    }
}

# Copy installation files
$installFiles = @("install-windows.bat", "install-mac-linux.sh", "nodejs-installer.bat")
foreach ($file in $installFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $packageName
    }
}

# Copy documentation
$docFiles = @("README.md", "INSTALLATION-GUIDE.md", "USER-MANUAL.md", "DEPLOYMENT-README.md", ".env.local.example")
foreach ($file in $docFiles) {
    if (Test-Path $file) {
        Copy-Item $file -Destination $packageName
    }
}

# Ensure environment template exists
if (-not (Test-Path "$packageName\.env.local.example")) {
    Write-Host "Creating environment template..." -ForegroundColor Yellow
    Copy-Item ".env.local.example" -Destination $packageName -ErrorAction SilentlyContinue
}

# Create startup script
$startScript = "$packageName\Start-SwingTrader-AI.bat"
$startContent = "@echo off`ntitle SwingTrader AI`necho Starting SwingTrader AI...`ntimeout /t 3 >nul`nstart http://localhost:3000`ncall npm start"
$startContent | Out-File -FilePath $startScript -Encoding ASCII

# Create update script
$updateScript = "$packageName\Update-SwingTrader-AI.bat"
$updateContent = "@echo off`ntitle SwingTrader AI - Update`necho Updating...`ncall npm install`ncall npm run build`necho Update complete!`npause"
$updateContent | Out-File -FilePath $updateScript -Encoding ASCII

# Create comprehensive quick start guide
$quickStart = @"
🚀 SwingTrader AI - Professional Trading Platform
================================================================

📋 QUICK INSTALLATION (Windows):
1. Double-click: install-windows.bat
2. Follow the automated setup wizard
3. Add your API keys when prompted
4. Application launches automatically at http://localhost:3000

🔑 API KEYS NEEDED:
- Polygon.io (Market Data): https://polygon.io/ - Free tier available
- OpenAI (AI Analysis): https://platform.openai.com/ - ~$10-50/month
- Optional: FMP, Alpaca for additional features

💰 ESTIMATED MONTHLY COST: $10-50 for typical usage

📖 NEED MORE HELP?
- Read INSTALLATION-GUIDE.md for detailed instructions
- Check USER-MANUAL.md for trading strategies
- See README.md for complete overview

⚙️ SYSTEM REQUIREMENTS:
- Windows 10/11
- 4GB RAM (8GB recommended)
- Internet connection
- Node.js (installer will help you get this)

🎯 READY TO START PROFESSIONAL SWING TRADING!
"@
$quickStart | Out-File -FilePath "$packageName\QUICK-START.txt" -Encoding UTF8

# Clean up development files
$cleanupDirs = @("node_modules", ".next", "build", "dist")
foreach ($dir in $cleanupDirs) {
    $fullPath = "$packageName\$dir"
    if (Test-Path $fullPath) {
        Remove-Item -Path $fullPath -Recurse -Force
    }
}

Write-Host "Creating ZIP package..." -ForegroundColor Blue
try {
    Compress-Archive -Path $packageName -DestinationPath "$packageName.zip" -Force
    $zipSize = (Get-Item "$packageName.zip").Length / 1MB
    Write-Host "Package created successfully!" -ForegroundColor Green
    Write-Host "ZIP File: $packageName.zip" -ForegroundColor Cyan
    Write-Host "Size: $([math]::Round($zipSize, 1)) MB" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to create ZIP package" -ForegroundColor Red
    Write-Host "Folder package available: $packageName" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "DEPLOYMENT PACKAGE READY!" -ForegroundColor Green
Write-Host "Share the ZIP file with users for one-click installation" -ForegroundColor White
Read-Host "Press Enter to continue"
