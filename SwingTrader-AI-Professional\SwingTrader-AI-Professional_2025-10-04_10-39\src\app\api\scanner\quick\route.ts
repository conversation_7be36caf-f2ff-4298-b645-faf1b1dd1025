import { NextRequest, NextResponse } from 'next/server'
import { swingScanner } from '@/lib/swingScanner'
import { PRIORITY_SYMBOLS } from '@/data/watchlist'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    
    console.log(`Starting quick scan of ${PRIORITY_SYMBOLS.length} priority stocks...`)
    
    const results = await swingScanner.quickScan(PRIORITY_SYMBOLS)
    
    // Limit results if requested
    const limitedResults = results.slice(0, limit)
    
    return NextResponse.json({
      totalScanned: PRIORITY_SYMBOLS.length,
      results: limitedResults,
      scanTime: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error in quick scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform quick stock scan' },
      { status: 500 }
    )
  }
}
