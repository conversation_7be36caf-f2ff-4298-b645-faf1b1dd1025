import { NextRequest, NextResponse } from 'next/server'

// Mock test data to verify the UI works when there are valid setups
export async function GET(request: NextRequest) {
  try {
    // Simulate a successful scan with mock data
    const mockResults = [
      {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        sector: 'Technology',
        quote: {
          symbol: 'AAPL',
          name: 'Apple Inc.',
          price: 175.50,
          change: 2.25,
          changePercent: 1.30,
          volume: 45000000,
          marketCap: 2800000000000,
          pe: 28.5,
          dividend: 0.24
        },
        overnightSetup: {
          strategy: 'overnight_momentum',
          score: 85,
          confidence: 'HIGH',
          entry: 175.50,
          stopLoss: 172.00,
          target1: 180.00,
          target2: 185.00,
          riskReward: 2.57,
          positionSize: 285,
          riskAmount: 997.50,
          signals: [
            'Strong overnight gap up',
            'Above 8 EMA',
            'High volume confirmation',
            'Bullish momentum'
          ],
          timeframe: '1-3 days',
          marketCondition: 'Bullish trend continuation'
        },
        bestStrategy: 'overnight_momentum',
        overallScore: 85,
        rank: 1,
        scanTime: new Date().toISOString(),
        alerts: ['High volume breakout'],
        riskWarnings: []
      },
      {
        symbol: 'NVDA',
        name: 'NVIDIA Corporation',
        sector: 'Technology',
        quote: {
          symbol: 'NVDA',
          name: 'NVIDIA Corporation',
          price: 485.20,
          change: 8.75,
          changePercent: 1.84,
          volume: 35000000,
          marketCap: 1200000000000,
          pe: 65.2,
          dividend: 0.16
        },
        breakoutSetup: {
          strategy: 'technical_breakout',
          score: 78,
          confidence: 'HIGH',
          entry: 485.20,
          stopLoss: 475.00,
          target1: 500.00,
          target2: 515.00,
          riskReward: 2.93,
          positionSize: 98,
          riskAmount: 999.60,
          signals: [
            'Breakout above resistance',
            'Volume surge',
            'RSI momentum',
            'Moving average support'
          ],
          timeframe: '2-5 days',
          marketCondition: 'Technical breakout pattern'
        },
        bestStrategy: 'technical_breakout',
        overallScore: 78,
        rank: 2,
        scanTime: new Date().toISOString(),
        alerts: ['Technical breakout confirmed'],
        riskWarnings: []
      },
      {
        symbol: 'TSLA',
        name: 'Tesla, Inc.',
        sector: 'Consumer Discretionary',
        quote: {
          symbol: 'TSLA',
          name: 'Tesla, Inc.',
          price: 245.80,
          change: 5.20,
          changePercent: 2.16,
          volume: 28000000,
          marketCap: 780000000000,
          pe: 45.8,
          dividend: 0
        },
        overnightSetup: {
          strategy: 'overnight_momentum',
          score: 72,
          confidence: 'MEDIUM',
          entry: 245.80,
          stopLoss: 240.00,
          target1: 255.00,
          target2: 265.00,
          riskReward: 3.31,
          positionSize: 172,
          riskAmount: 996.80,
          signals: [
            'Gap up with volume',
            'Above key moving averages',
            'Momentum building',
            'Sector strength'
          ],
          timeframe: '1-4 days',
          marketCondition: 'Momentum continuation'
        },
        bestStrategy: 'overnight_momentum',
        overallScore: 72,
        rank: 3,
        scanTime: new Date().toISOString(),
        alerts: ['Strong momentum signal'],
        riskWarnings: ['High volatility stock']
      }
    ]

    const summary = {
      totalScanned: 16,
      validSetups: 3,
      avgScore: 78.3,
      topSector: 'Technology',
      scanDuration: '2.1s',
      timestamp: new Date().toISOString(),
      results: mockResults
    }

    return NextResponse.json(summary)
  } catch (error) {
    console.error('Error in test scanner:', error)
    return NextResponse.json(
      { error: 'Failed to run test scan' },
      { status: 500 }
    )
  }
}
