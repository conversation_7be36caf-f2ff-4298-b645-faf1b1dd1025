'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import {
  <PERSON>, TrendingUp, AlertTriangle, Brain, Play, CheckCircle,
  ChevronDown, ChevronUp, Target, DollarSign, Clock, BarChart3,
  Loader2, Zap, Cpu
} from 'lucide-react'
import { EnhancedScanResult } from '@/lib/enhancedSwingScanner'
import { PaperTrade, AIAnalysis, TradingCardState } from '@/types/paperTrading'
import { formatCurrency, formatPercentage } from '@/lib/utils'

interface TradingCardProps {
  result: EnhancedScanResult
  accountSize: number
  onPaperTradeExecuted?: (trade: PaperTrade) => void
}

// Helper function to determine AI exposure level
function getAIExposureLevel(symbol: string): { level: 'Primary' | 'Secondary' | 'Adjacent' | null, icon: any, color: string } {
  // Core AI/Tech Giants - Direct AI/ML focus
  const primaryAI = ['MSFT', 'NVDA', 'GOOG', 'GOOGL', 'META', 'AVGO', 'ORCL', 'NFLX', 'AMD', 'TSM']

  // AI Infrastructure & Semiconductors
  const secondaryAI = ['ANET', 'MU', 'LRCX', 'DELL', 'WDC', 'VRT', 'ZS', 'NET', 'SHOP']

  // Quantum Computing & Advanced AI
  const quantumAI = ['IONQ', 'RGTI', 'SOUN', 'QBTS']

  // AI-Adjacent Growth Stocks
  const adjacentAI = [
    'AAPL', 'AMZN', 'JPM', 'GE', 'CAT', 'BA', 'C', 'SCHW', 'UAL', 'DAL',
    'CCJ', 'CEG', 'VST', 'FSLR', 'RUN', 'GILD', 'RBLX', 'RDDT',
    'HOOD', 'SOFI', 'AFRM', 'FUTU', 'TIGR', 'RKLB', 'ASTS',
    'RIOT', 'HUT', 'CELH', 'CVNA', 'ELF', 'W', 'ETSY',
    'KGC', 'AEM', 'NEM', 'HL', 'AG', 'CLF', 'MP', 'BTU', 'CG'
  ]

  if (primaryAI.includes(symbol)) {
    return { level: 'Primary', icon: Brain, color: 'bg-purple-500/20 text-purple-400 border-purple-500/30' }
  }
  if (secondaryAI.includes(symbol)) {
    return { level: 'Secondary', icon: Cpu, color: 'bg-blue-500/20 text-blue-400 border-blue-500/30' }
  }
  if (quantumAI.includes(symbol)) {
    return { level: 'Quantum', icon: Zap, color: 'bg-pink-500/20 text-pink-400 border-pink-500/30' }
  }
  if (adjacentAI.includes(symbol)) {
    return { level: 'Adjacent', icon: Target, color: 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30' }
  }
  return { level: null, icon: null, color: '' }
}

export function TradingCard({ result, accountSize, onPaperTradeExecuted }: TradingCardProps) {
  const [state, setState] = useState<TradingCardState>({
    isExecuting: false,
    isAnalyzing: false,
    showAnalysis: false
  })

  const setup = result.overnightSetup || result.breakoutSetup
  const strategyType = result.overnightSetup ? 'overnight_momentum' : 'technical_breakout'
  const strategyColor = result.overnightSetup ? 'purple' : 'green'
  const StrategyIcon = result.overnightSetup ? Moon : TrendingUp
  const aiExposure = getAIExposureLevel(result.symbol)

  const handleExecutePaperTrade = async () => {
    setState(prev => ({ ...prev, isExecuting: true }))
    
    try {
      const response = await fetch('/api/paper-trading/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scanResult: result, accountSize })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to execute paper trade')
      }

      const { paperTrade, executionDetails } = await response.json()
      
      setState(prev => ({ 
        ...prev, 
        paperTrade,
        isExecuting: false 
      }))

      onPaperTradeExecuted?.(paperTrade)
      
      // Show success notification
      alert(`Paper trade executed successfully!\n\nSymbol: ${paperTrade.symbol}\nPosition: ${paperTrade.positionSize} shares\nEntry: ${formatCurrency(paperTrade.entryPrice)}\nRisk: ${formatCurrency(paperTrade.riskAmount)}`)
      
    } catch (error) {
      console.error('Paper trade execution failed:', error)
      alert(`Failed to execute paper trade: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setState(prev => ({ ...prev, isExecuting: false }))
    }
  }

  const handleGetAIAnalysis = async () => {
    setState(prev => ({ ...prev, isAnalyzing: true }))
    
    try {
      const response = await fetch('/api/analysis/ai-setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ scanResult: result })
      })

      if (!response.ok) {
        throw new Error('Failed to get AI analysis')
      }

      const aiAnalysis: AIAnalysis = await response.json()
      
      setState(prev => ({ 
        ...prev, 
        aiAnalysis,
        isAnalyzing: false,
        showAnalysis: true 
      }))
      
    } catch (error) {
      console.error('AI analysis failed:', error)
      alert('Failed to get AI analysis. Please try again.')
      setState(prev => ({ ...prev, isAnalyzing: false }))
    }
  }

  if (!setup) return null

  return (
    <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors">
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-white mr-3">{result.symbol}</span>
              <Badge className={`${strategyColor === 'purple' ? 'bg-purple-500/20 text-purple-400' : 'bg-green-500/20 text-green-400'}`}>
                #{result.rank}
              </Badge>
              {aiExposure.level && (
                <Badge variant="outline" className={`ml-2 ${aiExposure.color}`}>
                  <aiExposure.icon className="mr-1 h-3 w-3" />
                  {aiExposure.level} AI
                </Badge>
              )}
            </div>
            <div className="text-sm text-slate-300 ml-3">{result.name}</div>
          </div>
          <div className="text-right">
            <div className="text-xl font-bold text-white">
              {result.overallScore.toFixed(1)}/100
            </div>
            <div className="text-sm text-slate-300">Confidence</div>
          </div>
        </div>

        {/* Strategy Details */}
        <div className={`mb-4 p-4 bg-${strategyColor}-900/20 rounded border border-${strategyColor}-500/30`}>
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <StrategyIcon className={`mr-2 h-4 w-4 text-${strategyColor}-400`} />
              <span className={`text-${strategyColor}-400 font-semibold`}>
                {result.overnightSetup ? 'Overnight Momentum Strategy' : 'Technical Breakout Strategy'}
              </span>
              <Badge className={`ml-2 bg-${strategyColor}-500/20 text-${strategyColor}-400`}>
                {setup.confidence.toFixed(1)}% confidence
              </Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm mb-4">
            <div>
              <div className="text-slate-300">Entry</div>
              <div className="text-white font-semibold">{formatCurrency(setup.entryPrice)}</div>
            </div>
            <div>
              <div className="text-slate-300">Stop</div>
              <div className="text-red-400 font-semibold">{formatCurrency(setup.stopLoss)}</div>
            </div>
            <div>
              <div className="text-slate-300">Target</div>
              <div className="text-green-400 font-semibold">{formatCurrency(setup.targets[0])}</div>
            </div>
            <div>
              <div className="text-slate-300">Position Size</div>
              <div className="text-white font-semibold">{setup.positionSize.toLocaleString()} shares</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {!state.paperTrade ? (
              <Button
                onClick={handleExecutePaperTrade}
                disabled={state.isExecuting}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {state.isExecuting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Executing...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Execute Paper Trade
                  </>
                )}
              </Button>
            ) : (
              <Button disabled className="flex-1 bg-green-600 text-white">
                <CheckCircle className="mr-2 h-4 w-4" />
                Trade Executed
              </Button>
            )}

            <Button
              onClick={handleGetAIAnalysis}
              disabled={state.isAnalyzing}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              {state.isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Brain className="mr-2 h-4 w-4" />
                  AI Analysis
                </>
              )}
            </Button>

            {state.aiAnalysis && (
              <Button
                onClick={() => setState(prev => ({ ...prev, showAnalysis: !prev.showAnalysis }))}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300"
              >
                {state.showAnalysis ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            )}
          </div>
        </div>

        {/* Paper Trade Confirmation */}
        {state.paperTrade && (
          <div className="mb-4 p-3 bg-green-900/20 rounded border border-green-500/30">
            <div className="flex items-center mb-2">
              <CheckCircle className="mr-2 h-4 w-4 text-green-400" />
              <span className="text-green-400 font-semibold">Paper Trade Executed</span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
              <div>
                <span className="text-slate-300">Position Value: </span>
                <span className="text-white">{formatCurrency(state.paperTrade.entryPrice * state.paperTrade.positionSize)}</span>
              </div>
              <div>
                <span className="text-slate-300">Risk Amount: </span>
                <span className="text-red-400">{formatCurrency(state.paperTrade.riskAmount)}</span>
              </div>
              <div>
                <span className="text-slate-300">Risk %: </span>
                <span className="text-yellow-400">{state.paperTrade.riskPercentage.toFixed(1)}%</span>
              </div>
            </div>
          </div>
        )}

        {/* AI Analysis Section */}
        {state.aiAnalysis && state.showAnalysis && (
          <div className="mb-4 p-4 bg-blue-900/20 rounded border border-blue-500/30">
            <div className="flex items-center mb-3">
              <Brain className="mr-2 h-4 w-4 text-blue-400" />
              <span className="text-blue-400 font-semibold">AI Setup Analysis</span>
              <Badge className="ml-2 bg-blue-500/20 text-blue-400">
                {state.aiAnalysis.confidence}% AI Confidence
              </Badge>
            </div>
            
            <div className="space-y-3 text-sm">
              <div>
                <div className="text-slate-300 font-medium mb-1">Setup Explanation:</div>
                <div className="text-white whitespace-pre-line">{state.aiAnalysis.setupExplanation}</div>
              </div>
              
              {state.aiAnalysis.catalysts && state.aiAnalysis.catalysts.length > 0 && (
                <div>
                  <div className="text-slate-300 font-medium mb-1">Market Catalysts:</div>
                  <ul className="text-white list-disc list-inside">
                    {state.aiAnalysis.catalysts.map((catalyst, i) => (
                      <li key={i}>{catalyst}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {state.aiAnalysis.riskAssessment && (
                <div>
                  <div className="text-slate-300 font-medium mb-1">Risk Assessment:</div>
                  <div className="text-white whitespace-pre-line">{state.aiAnalysis.riskAssessment}</div>
                </div>
              )}
              
              {state.aiAnalysis.keyLevels && state.aiAnalysis.keyLevels.length > 0 && (
                <div>
                  <div className="text-slate-300 font-medium mb-1">Key Levels to Watch:</div>
                  <div className="flex flex-wrap gap-2">
                    {state.aiAnalysis.keyLevels.map((level, i) => (
                      <Badge key={i} className="bg-slate-700 text-slate-300">
                        {level}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex justify-between items-center pt-2 border-t border-slate-600">
                <div>
                  <span className="text-slate-300">Expected Timeframe: </span>
                  <span className="text-white">{state.aiAnalysis.timeframe}</span>
                </div>
                {state.aiAnalysis.lastUpdated && (
                  <div className="text-xs text-slate-400">
                    Updated: {new Date(state.aiAnalysis.lastUpdated).toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Alerts and Warnings */}
        {result.alerts.length > 0 && (
          <div className="mb-2">
            <div className="text-sm text-slate-300 mb-1">Trading Alerts:</div>
            {result.alerts.map((alert, i) => (
              <div key={i} className="text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded mb-1">
                {alert}
              </div>
            ))}
          </div>
        )}

        {result.riskWarnings.length > 0 && (
          <div>
            <div className="text-sm text-slate-300 mb-1">Risk Warnings:</div>
            {result.riskWarnings.map((warning, i) => (
              <div key={i} className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded mb-1 flex items-center">
                <AlertTriangle className="mr-1 h-3 w-3" />
                {warning}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
