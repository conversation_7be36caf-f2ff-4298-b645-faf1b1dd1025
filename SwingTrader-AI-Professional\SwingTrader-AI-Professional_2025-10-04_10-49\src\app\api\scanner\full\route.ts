import { NextRequest, NextResponse } from 'next/server'
import { swingScanner } from '@/lib/swingScanner'
import { ALL_SYMBOLS } from '@/data/watchlist'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const maxConcurrent = parseInt(searchParams.get('concurrent') || '5')
    
    console.log(`Starting full scan of ${ALL_SYMBOLS.length} stocks...`)
    
    const scanSummary = await swingScanner.scanStocks(ALL_SYMBOLS, maxConcurrent)
    
    // Limit results if requested
    const limitedResults = {
      ...scanSummary,
      topOpportunities: scanSummary.topOpportunities.slice(0, limit)
    }
    
    return NextResponse.json(limitedResults)
  } catch (error) {
    console.error('Error in full scanner API:', error)
    return NextResponse.json(
      { error: 'Failed to perform full stock scan' },
      { status: 500 }
    )
  }
}
