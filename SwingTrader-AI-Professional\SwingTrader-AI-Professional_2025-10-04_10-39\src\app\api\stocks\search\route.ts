import { NextRequest, NextResponse } from 'next/server'
import { PolygonAPI } from '@/lib/polygon'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const limit = parseInt(searchParams.get('limit') || '10')
    
    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      )
    }

    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
    const results = await polygonAPI.searchStocks(query, limit)
    
    return NextResponse.json(results)
  } catch (error) {
    console.error('Error in search API:', error)
    return NextResponse.json(
      { error: 'Failed to search stocks' },
      { status: 500 }
    )
  }
}
