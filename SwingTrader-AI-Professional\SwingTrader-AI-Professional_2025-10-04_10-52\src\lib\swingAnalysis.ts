import { CandlestickData, SwingTradingAnalysis, TechnicalIndicator } from '@/types/trading'
import { TechnicalIndicators } from './indicators'
import { calculateRiskReward } from './utils'

export class SwingTradingAnalyzer {
  static analyzeSwingTrade(
    symbol: string,
    candles: CandlestickData[],
    timeframe: string = '1D'
  ): SwingTradingAnalysis {
    if (candles.length < 50) {
      throw new Error('Insufficient data for swing trading analysis')
    }

    const closes = candles.map(c => c.close)
    const highs = candles.map(c => c.high)
    const lows = candles.map(c => c.low)
    const currentPrice = closes[closes.length - 1]

    // Get technical indicators
    const indicators = TechnicalIndicators.analyzeSwingSetup(candles)
    
    // Find support and resistance levels
    const { support, resistance } = TechnicalIndicators.findSupportResistance(candles)
    
    // Determine trend
    const trend = this.determineTrend(candles)
    
    // Calculate entry, stop loss, and take profit levels
    const levels = this.calculateTradingLevels(candles, trend)
    
    // Calculate confidence score
    const confidence = this.calculateConfidence(indicators, trend, levels)
    
    // Generate recommendation
    const recommendation = this.generateRecommendation(indicators, confidence, trend)
    
    // Generate analysis text
    const analysis = this.generateAnalysisText(symbol, indicators, trend, levels, confidence)

    return {
      symbol,
      timeframe,
      trend: trend.direction,
      confidence,
      entryPrice: levels.entry,
      stopLoss: levels.stopLoss,
      takeProfit: levels.takeProfit,
      riskRewardRatio: calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit),
      indicators,
      supportLevels: support.slice(-3), // Last 3 support levels
      resistanceLevels: resistance.slice(-3), // Last 3 resistance levels
      analysis,
      recommendation
    }
  }

  private static determineTrend(candles: CandlestickData[]): { direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS', strength: number } {
    const closes = candles.map(c => c.close)
    const sma20 = TechnicalIndicators.sma(closes, 20)
    const sma50 = TechnicalIndicators.sma(closes, 50)
    
    const currentPrice = closes[closes.length - 1]
    const currentSMA20 = sma20[sma20.length - 1]
    const currentSMA50 = sma50[sma50.length - 1]
    
    // Calculate trend strength based on price position relative to moving averages
    let strength = 0
    let direction: 'BULLISH' | 'BEARISH' | 'SIDEWAYS' = 'SIDEWAYS'
    
    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
      direction = 'BULLISH'
      strength = Math.min(((currentPrice / currentSMA50) - 1) * 100, 100)
    } else if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
      direction = 'BEARISH'
      strength = Math.min(((currentSMA50 / currentPrice) - 1) * 100, 100)
    } else {
      // Sideways trend
      const volatility = this.calculateVolatility(closes.slice(-20))
      strength = Math.max(0, 50 - volatility * 10)
    }
    
    return { direction, strength: Math.abs(strength) }
  }

  private static calculateTradingLevels(candles: CandlestickData[], trend: { direction: string, strength: number }) {
    const closes = candles.map(c => c.close)
    const highs = candles.map(c => c.high)
    const lows = candles.map(c => c.low)
    const currentPrice = closes[closes.length - 1]
    
    // Calculate ATR for stop loss placement
    const atr = this.calculateATR(candles, 14)
    const currentATR = atr[atr.length - 1]
    
    let entry = currentPrice
    let stopLoss: number
    let takeProfit: number
    
    if (trend.direction === 'BULLISH') {
      // For bullish trend, look for pullback entry
      const recentLow = Math.min(...lows.slice(-10))
      entry = Math.max(recentLow * 1.005, currentPrice * 0.995) // Slight pullback entry
      stopLoss = entry - (currentATR * 2)
      takeProfit = entry + (currentATR * 3) // 1.5:1 risk-reward
    } else if (trend.direction === 'BEARISH') {
      // For bearish trend, look for bounce entry (short)
      const recentHigh = Math.max(...highs.slice(-10))
      entry = Math.min(recentHigh * 0.995, currentPrice * 1.005) // Slight bounce entry
      stopLoss = entry + (currentATR * 2)
      takeProfit = entry - (currentATR * 3) // 1.5:1 risk-reward
    } else {
      // Sideways trend - range trading
      const recentHigh = Math.max(...highs.slice(-20))
      const recentLow = Math.min(...lows.slice(-20))
      const midpoint = (recentHigh + recentLow) / 2
      
      if (currentPrice < midpoint) {
        // Near support, look for bounce
        entry = currentPrice
        stopLoss = recentLow * 0.995
        takeProfit = recentHigh * 0.995
      } else {
        // Near resistance, look for rejection
        entry = currentPrice
        stopLoss = recentHigh * 1.005
        takeProfit = recentLow * 1.005
      }
    }
    
    return { entry, stopLoss, takeProfit }
  }

  private static calculateATR(candles: CandlestickData[], period: number): number[] {
    const trueRanges: number[] = []
    
    for (let i = 1; i < candles.length; i++) {
      const high = candles[i].high
      const low = candles[i].low
      const prevClose = candles[i - 1].close
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      )
      
      trueRanges.push(tr)
    }
    
    return TechnicalIndicators.sma(trueRanges, period)
  }

  private static calculateVolatility(prices: number[]): number {
    const returns = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
    
    return Math.sqrt(variance) * Math.sqrt(252) // Annualized volatility
  }

  private static calculateConfidence(
    indicators: TechnicalIndicator[],
    trend: { direction: string, strength: number },
    levels: { entry: number, stopLoss: number, takeProfit: number }
  ): number {
    let confidence = 50 // Base confidence
    
    // Add confidence based on indicator alignment
    const bullishSignals = indicators.filter(ind => ind.signal === 'BUY').length
    const bearishSignals = indicators.filter(ind => ind.signal === 'SELL').length
    const totalSignals = indicators.length
    
    if (bullishSignals > bearishSignals) {
      confidence += (bullishSignals / totalSignals) * 30
    } else if (bearishSignals > bullishSignals) {
      confidence += (bearishSignals / totalSignals) * 30
    }
    
    // Add confidence based on trend strength
    confidence += (trend.strength / 100) * 20
    
    // Add confidence based on risk-reward ratio
    const riskReward = calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit)
    if (riskReward >= 2) {
      confidence += 10
    } else if (riskReward >= 1.5) {
      confidence += 5
    }
    
    return Math.min(Math.max(confidence, 0), 100)
  }

  private static generateRecommendation(
    indicators: TechnicalIndicator[],
    confidence: number,
    trend: { direction: string, strength: number }
  ): 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' | 'NO_TRADE' {
    if (confidence < 40) {
      return 'NO_TRADE'
    }
    
    const bullishSignals = indicators.filter(ind => ind.signal === 'BUY').length
    const bearishSignals = indicators.filter(ind => ind.signal === 'SELL').length
    
    if (bullishSignals > bearishSignals && confidence >= 70) {
      return 'STRONG_BUY'
    } else if (bullishSignals > bearishSignals && confidence >= 50) {
      return 'BUY'
    } else if (bearishSignals > bullishSignals && confidence >= 70) {
      return 'STRONG_SELL'
    } else if (bearishSignals > bullishSignals && confidence >= 50) {
      return 'SELL'
    } else {
      return 'HOLD'
    }
  }

  private static generateAnalysisText(
    symbol: string,
    indicators: TechnicalIndicator[],
    trend: { direction: string, strength: number },
    levels: { entry: number, stopLoss: number, takeProfit: number },
    confidence: number
  ): string {
    const riskReward = calculateRiskReward(levels.entry, levels.stopLoss, levels.takeProfit)
    
    let analysis = `${symbol} Swing Trading Analysis:\n\n`
    
    analysis += `Trend: ${trend.direction} (Strength: ${trend.strength.toFixed(1)}%)\n`
    analysis += `Confidence: ${confidence.toFixed(1)}%\n\n`
    
    analysis += `Entry: $${levels.entry.toFixed(2)}\n`
    analysis += `Stop Loss: $${levels.stopLoss.toFixed(2)}\n`
    analysis += `Take Profit: $${levels.takeProfit.toFixed(2)}\n`
    analysis += `Risk/Reward: ${riskReward.toFixed(2)}:1\n\n`
    
    analysis += `Technical Indicators:\n`
    indicators.forEach(indicator => {
      analysis += `• ${indicator.description}\n`
    })
    
    return analysis
  }
}
