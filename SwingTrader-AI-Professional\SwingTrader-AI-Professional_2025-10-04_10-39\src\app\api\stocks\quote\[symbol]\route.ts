import { NextRequest, NextResponse } from 'next/server'
import { PolygonAPI } from '@/lib/polygon'
import { IBKRAPI } from '@/lib/ibkr'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params

    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    // Initialize APIs
    const ibkrAPI = new IBKRAPI({
      host: '127.0.0.1',
      port: 7497,
      clientId: 2, // Different client ID for API route
      paperTrading: true
    })
    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)

    // Try IBKR first
    try {
      console.log(`📊 Using IBKR TWS for ${symbol} quote (no rate limits)...`)

      if (!ibkrAPI.isConnected()) {
        await ibkrAPI.connect()
      }

      const quote = await ibkrAPI.getStockQuote(symbol.toUpperCase())
      return NextResponse.json(quote)
    } catch (ibkrError) {
      console.error(`IBKR API error for ${symbol}:`, ibkrError)

      // Fallback to Polygon
      try {
        console.log(`⚠️ IBKR failed for ${symbol}, trying Polygon fallback...`)
        const quote = await polygonAPI.getStockQuote(symbol.toUpperCase())
        return NextResponse.json(quote)
      } catch (polygonError) {
        console.error(`Both IBKR and Polygon failed for ${symbol}:`, polygonError)
        throw new Error('All data sources failed')
      }
    }
  } catch (error) {
    console.error('Error in quote API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stock quote' },
      { status: 500 }
    )
  }
}
