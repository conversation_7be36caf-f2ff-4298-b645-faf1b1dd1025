import Alpaca from '@alpacahq/alpaca-trade-api'

export class AlpacaAPI {
  private client: Alpaca

  constructor() {
    const apiKey = process.env.ALPACA_API_KEY || 'PKKKYLNNZZT2EI7F3CVL'
    const secretKey = process.env.ALPACA_SECRET_KEY || 'Bgh3CLNSueS9Odyeb6U38UddNEluGDSIflunjinD'
    const baseUrl = process.env.ALPACA_BASE_URL || 'https://paper-api.alpaca.markets'

    console.log('🔑 Alpaca API Key prefix:', apiKey.substring(0, 10))
    console.log('🔑 Source:', process.env.ALPACA_API_KEY ? 'Environment Variable' : 'Hardcoded Fallback')

    this.client = new Alpaca({
      keyId: apiKey,
      secretKey: secretKey,
      baseUrl: baseUrl,
      usePolygon: false
    })
  }

  // Get account information
  async getAccount() {
    try {
      return await this.client.getAccount()
    } catch (error) {
      console.error('Error fetching account:', error)
      throw error
    }
  }

  // Get account positions
  async getPositions() {
    try {
      return await this.client.getPositions()
    } catch (error) {
      console.error('Error fetching positions:', error)
      throw error
    }
  }

  // Get orders
  async getOrders(status?: 'open' | 'closed' | 'all') {
    try {
      return await this.client.getOrders({
        status: status || 'all',
        limit: 100,
        nested: true
      })
    } catch (error) {
      console.error('Error fetching orders:', error)
      throw error
    }
  }

  // Place a market order
  async placeMarketOrder(
    symbol: string,
    qty: number,
    side: 'buy' | 'sell',
    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'
  ) {
    try {
      return await this.client.createOrder({
        symbol,
        qty,
        side,
        type: 'market',
        time_in_force: timeInForce
      })
    } catch (error) {
      console.error('Error placing market order:', error)
      throw error
    }
  }

  // Place a limit order
  async placeLimitOrder(
    symbol: string,
    qty: number,
    side: 'buy' | 'sell',
    limitPrice: number,
    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'
  ) {
    try {
      return await this.client.createOrder({
        symbol,
        qty,
        side,
        type: 'limit',
        limit_price: limitPrice,
        time_in_force: timeInForce
      })
    } catch (error) {
      console.error('Error placing limit order:', error)
      throw error
    }
  }

  // Place a stop-loss order
  async placeStopLossOrder(
    symbol: string,
    qty: number,
    side: 'buy' | 'sell',
    stopPrice: number,
    timeInForce: 'day' | 'gtc' | 'ioc' | 'fok' = 'day'
  ) {
    try {
      return await this.client.createOrder({
        symbol,
        qty,
        side,
        type: 'stop',
        stop_price: stopPrice,
        time_in_force: timeInForce
      })
    } catch (error) {
      console.error('Error placing stop-loss order:', error)
      throw error
    }
  }

  // Place a bracket order (entry + stop loss + take profit)
  async placeBracketOrder(
    symbol: string,
    qty: number,
    side: 'buy' | 'sell',
    limitPrice: number,
    stopLoss: number,
    takeProfit: number,
    timeInForce: 'day' | 'gtc' = 'day'
  ) {
    try {
      return await this.client.createOrder({
        symbol,
        qty,
        side,
        type: 'limit',
        limit_price: limitPrice,
        time_in_force: timeInForce,
        order_class: 'bracket',
        stop_loss: {
          stop_price: stopLoss
        },
        take_profit: {
          limit_price: takeProfit
        }
      })
    } catch (error) {
      console.error('Error placing bracket order:', error)
      throw error
    }
  }

  // Cancel an order
  async cancelOrder(orderId: string) {
    try {
      return await this.client.cancelOrder(orderId)
    } catch (error) {
      console.error('Error canceling order:', error)
      throw error
    }
  }

  // Cancel all orders
  async cancelAllOrders() {
    try {
      return await this.client.cancelAllOrders()
    } catch (error) {
      console.error('Error canceling all orders:', error)
      throw error
    }
  }

  // Get portfolio history
  async getPortfolioHistory(period?: '1D' | '7D' | '1M' | '3M' | '1Y' | 'all') {
    try {
      return await this.client.getPortfolioHistory({
        period: period || '1M',
        timeframe: '1Day'
      })
    } catch (error) {
      console.error('Error fetching portfolio history:', error)
      throw error
    }
  }

  // Get market calendar
  async getMarketCalendar(start?: string, end?: string) {
    try {
      return await this.client.getCalendar({
        start,
        end
      })
    } catch (error) {
      console.error('Error fetching market calendar:', error)
      throw error
    }
  }

  // Check if market is open
  async isMarketOpen() {
    try {
      const clock = await this.client.getClock()
      return clock.is_open
    } catch (error) {
      console.error('Error checking market status:', error)
      return false
    }
  }

  // Get buying power
  async getBuyingPower() {
    try {
      const account = await this.getAccount()
      return parseFloat(account.buying_power)
    } catch (error) {
      console.error('Error fetching buying power:', error)
      return 0
    }
  }

  // Calculate position size based on risk
  calculatePositionSize(
    accountValue: number,
    riskPercentage: number,
    entryPrice: number,
    stopLoss: number
  ): number {
    const riskAmount = accountValue * (riskPercentage / 100)
    const riskPerShare = Math.abs(entryPrice - stopLoss)
    return Math.floor(riskAmount / riskPerShare)
  }
}

// Create a singleton instance
export const alpacaAPI = new AlpacaAPI()
