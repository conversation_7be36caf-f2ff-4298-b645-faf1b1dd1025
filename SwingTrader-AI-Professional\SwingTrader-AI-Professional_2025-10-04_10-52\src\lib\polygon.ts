import axios from 'axios'
import { CandlestickData, StockData } from '@/types/trading'

const POLYGON_BASE_URL = 'https://api.polygon.io'
const API_KEY = process.env.POLYGON_API_KEY || '********************************'

export class PolygonAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || '********************************'
    console.log('🔑 Polygon API Key prefix:', this.apiKey.substring(0, 15))
    console.log('🔑 Source:', process.env.POLYGON_API_KEY ? 'Environment Variable' : 'Hardcoded Fallback')
  }

  // Get current stock quote using Polygon's snapshot endpoint (better for paid plans)
  async getStockQuote(symbol: string): Promise<StockData> {
    try {
      // Use snapshot endpoint for real-time data (available on paid plans)
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v2/snapshot/locale/us/markets/stocks/tickers/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      console.log(`🔍 Polygon API response for ${symbol}:`, response.data)

      // Handle different Polygon API response formats
      let ticker
      if (response.data.results && response.data.results.length > 0) {
        // Standard format: { results: [{ ... }] }
        ticker = response.data.results[0]
      } else if (response.data.ticker) {
        // Alternative format: { ticker: { ... } }
        ticker = response.data.ticker
      } else {
        throw new Error(`No data found for ${symbol} - Polygon API returned unexpected format`)
      }

      if (!ticker) {
        throw new Error(`No data found for ${symbol}`)
      }
      const dayData = ticker.day || {}
      const prevDayData = ticker.prevDay || {}
      const lastQuote = ticker.lastQuote || {}
      const lastTrade = ticker.lastTrade || {}

      // Use the most recent price available
      const currentPrice = lastTrade.p || dayData.c || prevDayData.c
      const prevClose = prevDayData.c || dayData.o
      const change = currentPrice - prevClose
      const changePercent = (change / prevClose) * 100

      return {
        symbol: symbol.toUpperCase(),
        name: ticker.name || symbol.toUpperCase(),
        price: currentPrice,
        change,
        changePercent,
        volume: dayData.v || 0,
        marketCap: ticker.market_cap,
        pe: undefined,
        dividend: undefined
      }
    } catch (error) {
      console.error('Error fetching stock quote from Polygon:', error)

      // Fallback to previous day data if snapshot fails
      try {
        const fallbackResponse = await axios.get(
          `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/prev`,
          {
            params: {
              adjusted: 'true',
              apikey: this.apiKey
            }
          }
        )

        const data = fallbackResponse.data.results[0]
        return {
          symbol: symbol.toUpperCase(),
          name: symbol.toUpperCase(),
          price: data.c,
          change: data.c - data.o,
          changePercent: ((data.c - data.o) / data.o) * 100,
          volume: data.v,
          marketCap: undefined,
          pe: undefined,
          dividend: undefined
        }
      } catch (fallbackError) {
        console.error('Polygon fallback also failed:', fallbackError)
        throw new Error(`Failed to fetch quote for ${symbol}`)
      }
    }
  }

  // Get historical candlestick data (optimized for paid plans)
  async getHistoricalData(
    symbol: string,
    timespan: 'minute' | 'hour' | 'day' | 'week' | 'month' = 'day',
    multiplier: number = 1,
    from: string,
    to: string
  ): Promise<CandlestickData[]> {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v2/aggs/ticker/${symbol}/range/${multiplier}/${timespan}/${from}/${to}`,
        {
          params: {
            adjusted: 'true',
            sort: 'asc',
            limit: 50000, // Higher limit for paid plans
            apikey: this.apiKey
          }
        }
      )

      if (!response.data.results || response.data.results.length === 0) {
        console.warn(`No historical data found for ${symbol}`)
        return []
      }

      return response.data.results.map((candle: any) => ({
        timestamp: candle.t,
        open: candle.o,
        high: candle.h,
        low: candle.l,
        close: candle.c,
        volume: candle.v
      }))
    } catch (error) {
      console.error(`Error fetching historical data for ${symbol}:`, error)

      // Log the specific error for debugging
      if (error.response) {
        console.error(`Polygon API Error: ${error.response.status} - ${error.response.statusText}`)
        console.error('Response data:', error.response.data)
      }

      throw new Error(`Failed to fetch historical data for ${symbol}: ${error.message}`)
    }
  }

  // Get company details
  async getCompanyDetails(symbol: string) {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v3/reference/tickers/${symbol}`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data.results
    } catch (error) {
      console.error('Error fetching company details:', error)
      return null
    }
  }

  // Get market status
  async getMarketStatus() {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v1/marketstatus/now`,
        {
          params: {
            apikey: this.apiKey
          }
        }
      )

      return response.data
    } catch (error) {
      console.error('Error fetching market status:', error)
      return null
    }
  }

  // Search for stocks
  async searchStocks(query: string, limit: number = 10) {
    try {
      const response = await axios.get(
        `${POLYGON_BASE_URL}/v3/reference/tickers`,
        {
          params: {
            search: query,
            market: 'stocks',
            active: 'true',
            limit,
            apikey: this.apiKey
          }
        }
      )

      return response.data.results || []
    } catch (error) {
      console.error('Error searching stocks:', error)
      return []
    }
  }
}

// Create a singleton instance
export const polygonAPI = new PolygonAPI()
